# Vehicle Share Functionality

## Overview
The vehicle details page now includes a share functionality that generates a snapshot image of the vehicle details and shares it via the device's native sharing capabilities.

## Features

### 1. **Snapshot Generation**
- Creates a programmatic screenshot of a specially designed shareable vehicle card
- Includes essential vehicle information in a visually appealing format
- Optimized layout for sharing across different platforms

### 2. **Shareable Vehicle Card Components**
The generated snapshot includes:
- **Header**: 2ndCar branding with vehicle price prominently displayed
- **Vehicle Image**: Primary vehicle image
- **Key Details Grid**: Year, kilometers driven, fuel type, and ownership
- **Additional Details**: Brand, model, variant, location, and insurance information
- **Footer**: 2ndCar website reference

### 3. **Native Sharing Integration**
- Uses `expo-sharing` for enhanced sharing capabilities
- Falls back to React Native's built-in `Share` API if expo-sharing is not available
- Includes both image and text content in the share
- Maintains existing share title and message text

### 4. **Error Handling**
- Graceful fallback to clipboard copy if sharing fails
- User-friendly error messages
- Maintains app stability even if sharing libraries fail

## Technical Implementation

### Dependencies Added
```json
{
  "react-native-view-shot": "^3.8.0",
  "expo-sharing": "^12.0.1"
}
```

### Key Components

#### 1. **ShareableVehicleCard Component**
- Hidden component positioned off-screen for capture
- Styled specifically for sharing (350px width, optimized layout)
- Uses project's color palette and design system

#### 2. **Enhanced Share Function**
```typescript
const handleSharePress = async () => {
  // Capture the shareable view as an image
  const uri = await captureRef(shareableViewRef, {
    format: 'png',
    quality: 0.8,
    result: 'tmpfile',
  });

  // Share with native capabilities
  if (await Sharing.isAvailableAsync()) {
    await Sharing.shareAsync(uri, {
      mimeType: 'image/png',
      dialogTitle: shareData.title,
    });
  }
  // ... fallback logic
};
```

#### 3. **Styling System**
- Dedicated `shareableCardStyles` StyleSheet
- Consistent with project's design system
- Optimized for readability in shared images

### File Structure
```
app/(public)/vehicle/[id]/index.tsx - Main implementation
components/test/ShareableCardTest.tsx - Test component
docs/SHARE_FUNCTIONALITY.md - This documentation
```

## Usage

1. **User Experience**:
   - User taps the share button on vehicle details page
   - App generates a snapshot of the vehicle information
   - Native share dialog opens with the generated image
   - User can share to various apps (WhatsApp, social media, etc.)

2. **Fallback Behavior**:
   - If image sharing fails, falls back to text-only sharing
   - If all sharing fails, copies link to clipboard
   - User receives appropriate feedback for each scenario

## Design Considerations

### Visual Design
- **Brand Consistency**: Uses 2ndCar branding and color scheme
- **Information Hierarchy**: Price prominently displayed, key details easily scannable
- **Mobile Optimization**: Designed for mobile sharing and viewing
- **Professional Appearance**: Clean, modern design suitable for business use

### Performance
- **Efficient Rendering**: Hidden component only renders when needed
- **Optimized Image Quality**: 0.8 quality setting balances file size and clarity
- **Memory Management**: Temporary files are handled by the system

### Accessibility
- **Screen Reader Support**: All text elements are accessible
- **Color Contrast**: Meets accessibility standards
- **Touch Targets**: Adequate size for easy interaction

## Testing

### Test Component
A test component (`ShareableCardTest.tsx`) is available to verify the visual appearance of the shareable card without needing to navigate to a specific vehicle.

### Manual Testing Checklist
- [ ] Share button generates image correctly
- [ ] Image contains all expected vehicle information
- [ ] Native sharing dialog opens
- [ ] Fallback to text sharing works
- [ ] Clipboard fallback functions
- [ ] Error messages display appropriately

## Future Enhancements

### Potential Improvements
1. **Customizable Templates**: Multiple card designs for different sharing contexts
2. **Branding Options**: Dealer-specific branding for business users
3. **Social Media Optimization**: Platform-specific image dimensions
4. **Analytics**: Track sharing success rates and popular platforms
5. **Offline Support**: Cache images for offline sharing

### Performance Optimizations
1. **Image Caching**: Cache generated images to avoid regeneration
2. **Background Processing**: Generate images in background for faster sharing
3. **Compression Options**: Dynamic quality based on network conditions

## Troubleshooting

### Common Issues
1. **Image Generation Fails**: Check device storage and permissions
2. **Sharing Not Available**: Verify expo-sharing installation
3. **Layout Issues**: Ensure all required fonts and icons are loaded
4. **Performance**: Monitor memory usage on older devices

### Debug Mode
Enable debug logging by adding console statements in the share function to track the sharing flow.
