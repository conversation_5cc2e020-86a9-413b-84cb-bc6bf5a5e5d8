// app/index.tsx
import { useAppSelector } from '@/store/hooks';
import { Redirect } from 'expo-router';
import { useEffect, useState } from 'react';
import LoadingIndicator from '@/components/common/LoadingIndicator';
import { useNavigation } from '@/contexts/NavigationContext';

export default function Index() {
  const { isAuthenticated, loading } = useAppSelector((state) => state.auth);
  const { intendedDestination, clearIntendedDestination } = useNavigation();
  const [initialLoad, setInitialLoad] = useState(true);
  const [redirectPath, setRedirectPath] = useState<string | null>(null);

  useEffect(() => {
    // Give a moment for auth to initialize
    const timer = setTimeout(() => {
      setInitialLoad(false);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Handle redirect logic in useEffect to avoid setState during render
    if (!loading && !initialLoad) {
      if (isAuthenticated) {
        // If user just logged in and has an intended destination, redirect there
        if (intendedDestination) {
          clearIntendedDestination();
          setRedirectPath(intendedDestination);
        } else {
          setRedirectPath("/(auth)/(tabs)");
        }
      } else {
        setRedirectPath("/(public)/(tabs)");
      }
    }
  }, [isAuthenticated, loading, initialLoad, intendedDestination, clearIntendedDestination]);

  if (loading || initialLoad) {
    return <LoadingIndicator size={120} color1="#ff6b6b" color2="#4ecdc4" />;
  }

  if (redirectPath) {
    return <Redirect href={redirectPath as any} />;
  }

  // Fallback
  return <LoadingIndicator size={120} color1="#ff6b6b" color2="#4ecdc4" />;
}
