import React from 'react';
import { View, StyleSheet } from 'react-native';
import { ThemedText } from '@/components/common/ThemedText';
import ThemedButton from '@/components/common/ThemedButton';
import { COLORS, SPACING } from '@/constants/theme';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@/contexts/NavigationContext';

export default function PublicSaleScreen() {
  const { setIntendedDestination } = useNavigation();

  const handleLoginPress = () => {
    // Set intended destination before navigating to login
    setIntendedDestination('/(auth)/(tabs)/sale');
    router.push('/auth/register');
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Ionicons
          name="key-outline"
          size={80}
          color={COLORS.primary}
          style={styles.icon}
        />
        <ThemedText style={styles.title}>
          Sell Your Vehicle
        </ThemedText>
        <ThemedText style={styles.description}>
          To list your vehicle for sale, you need to be logged in to your account.
        </ThemedText>
        <ThemedButton
          onPress={handleLoginPress}
          style={styles.loginButton}
        >
          Login to Continue
        </ThemedButton>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.lg,
  },
  content: {
    alignItems: 'center',
    maxWidth: 300,
  },
  icon: {
    marginBottom: SPACING.xl,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: SPACING.md,
    color: COLORS.text.primary,
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: SPACING.xl,
    color: COLORS.text.secondary,
    lineHeight: 24,
  },
  loginButton: {
    width: '100%',
  },
});
