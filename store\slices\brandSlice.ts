import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { VehicleType } from "./vehicleSlice";
import axios from "axios";

const API_BASE_URL = process.env.EXPO_PUBLIC_API_BASE_URL;

export interface Brand {
  id: number;
  key: string;
  name: string;
  image: string;
  display_order: number;
  popular_order: number;
  image_url: string;
}

interface BrandState {
  brands: Brand[];
  loading: boolean;
  error: string | null;
}

const initialState: BrandState = {
  brands: [],
  loading: false,
  error: null,
};

export const fetchBrands = createAsyncThunk<
  Brand[],
  { type: VehicleType },
  { rejectValue: string }
>("brands/fetchBrands", async ({ type }, { rejectWithValue }) => {
  try {
    // Using Axios instead of fetch
    const response = await axios.get(`${API_BASE_URL}/display/brands`, {
      params: {
        type: type,
        limit: 10000,
        offset: 1,
      },
    });

    // Axios automatically throws errors for non-2xx responses
    // and parses JSO<PERSON> responses

    if (!response.data.status) {
      throw new Error(response.data.message || "Failed to fetch brands");
    }

    return response.data.data || [];
  } catch (error) {
    console.error("Fetch brands error:", error);

    // Handle Axios errors
    if (axios.isAxiosError(error)) {
      const errorMessage = error.response?.data?.message || error.message;
      return rejectWithValue(errorMessage);
    }

    return rejectWithValue(
      error instanceof Error ? error.message : "Failed to fetch brands"
    );
  }
});

const brandSlice = createSlice({
  name: "brands",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchBrands.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchBrands.fulfilled, (state, action) => {
        state.loading = false;
        state.brands = action.payload;
      })
      .addCase(fetchBrands.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch brands";
      });
  },
});

export default brandSlice.reducer;
