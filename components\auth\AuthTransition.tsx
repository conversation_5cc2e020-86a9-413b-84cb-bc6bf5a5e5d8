import React, { useEffect, useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { useAppSelector } from '@/store/hooks';
import LoadingIndicator from '@/components/common/LoadingIndicator';
import { ThemedText } from '@/components/common/ThemedText';
import { COLORS, SPACING } from '@/constants/theme';

interface AuthTransitionProps {
  children: React.ReactNode;
}

export default function AuthTransition({ children }: AuthTransitionProps) {
  const { isAuthenticated, loading } = useAppSelector((state) => state.auth);
  const [showTransition, setShowTransition] = useState(false);
  const [previousAuthState, setPreviousAuthState] = useState(isAuthenticated);

  useEffect(() => {
    // Show transition when auth state changes
    if (previousAuthState !== isAuthenticated && !loading) {
      setShowTransition(true);
      
      // Hide transition after a brief moment
      const timer = setTimeout(() => {
        setShowTransition(false);
        setPreviousAuthState(isAuthenticated);
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [isAuthenticated, loading, previousAuthState]);

  if (showTransition) {
    return (
      <View style={styles.transitionContainer}>
        <LoadingIndicator size={80} color1="#ff6b6b" color2="#4ecdc4" />
        <ThemedText style={styles.transitionText}>
          {isAuthenticated ? 'Welcome back!' : 'Signing out...'}
        </ThemedText>
      </View>
    );
  }

  return <>{children}</>;
}

const styles = StyleSheet.create({
  transitionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.background,
    padding: SPACING.lg,
  },
  transitionText: {
    marginTop: SPACING.lg,
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text.primary,
    textAlign: 'center',
  },
});
