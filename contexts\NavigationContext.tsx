import React, { createContext, useContext, useState, ReactNode } from 'react';

interface NavigationContextType {
  intendedDestination: string | null;
  setIntendedDestination: (destination: string | null) => void;
  clearIntendedDestination: () => void;
}

const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

interface NavigationProviderProps {
  children: ReactNode;
}

export function NavigationProvider({ children }: NavigationProviderProps) {
  const [intendedDestination, setIntendedDestination] = useState<string | null>(null);

  const clearIntendedDestination = () => {
    setIntendedDestination(null);
  };

  return (
    <NavigationContext.Provider
      value={{
        intendedDestination,
        setIntendedDestination,
        clearIntendedDestination,
      }}
    >
      {children}
    </NavigationContext.Provider>
  );
}

export function useNavigation() {
  const context = useContext(NavigationContext);
  if (context === undefined) {
    throw new Error('useNavigation must be used within a NavigationProvider');
  }
  return context;
}
