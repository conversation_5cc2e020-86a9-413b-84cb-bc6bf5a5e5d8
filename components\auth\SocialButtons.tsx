import React, { useEffect, useState } from 'react';
import { View, StyleSheet, Image, Alert, Platform, ActivityIndicator, TextInput } from 'react-native';
import * as Google from 'expo-auth-session/providers/google';
import * as Facebook from 'expo-auth-session/providers/facebook';
import * as WebBrowser from 'expo-web-browser';
import { makeRedirectUri, exchangeCodeAsync, ResponseType } from 'expo-auth-session';
import { GoogleAuthProvider, FacebookAuthProvider, signInWithCredential } from 'firebase/auth';
import ThemedButton from '@/components/common/ThemedButton';
import { ThemedText } from '@/components/common/ThemedText';
import { useRouter } from 'expo-router';
import { auth } from '../../config/firebase';
import { useAppDispatch } from '@/store/hooks';
import { registerWithGoogle, registerWithPhone } from '@/store/slices/authSlice';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Ionicons } from '@expo/vector-icons';

// Complete any in-progress auth session (important for web)
WebBrowser.maybeCompleteAuthSession();

// Replace with your API base URL or use environment variables.
const BASE_URL = process.env.EXPO_PUBLIC_API_BASE_URL;

export default function SocialButtons() {
    const [loading, setLoading] = useState(false);
    const [googleLoading, setGoogleLoading] = useState(false);
    const [facebookLoading, setFacebookLoading] = useState(false);
    const [phoneRequired, setPhoneRequired] = useState(false);
    const [pendingUid, setPendingUid] = useState(null);
    const [phone, setPhone] = useState('');
    const router = useRouter();
    const dispatch = useAppDispatch();

    // Configure the Google auth request.
    const [request, response, promptAsync] = Google.useAuthRequest({
        webClientId: process.env.EXPO_PUBLIC_WEB_CLIENT_ID,
        androidClientId: process.env.EXPO_PUBLIC_ANDROID_CLIENT_ID,
        iosClientId: process.env.EXPO_PUBLIC_IOS_CLIENT_ID,
        // Use 'code' response type for native (PKCE enabled)
        responseType: Platform.select({
            web: 'token',
            default: 'code'
        }),
        redirectUri: makeRedirectUri({
            scheme: 'com.factcoding.secondcar',
            path: ''
        }),
        scopes: ['profile', 'email']
    });

    // Configure the Facebook auth request.
    const [facebookRequest, facebookResponse, facebookPromptAsync] = Facebook.useAuthRequest({
        clientId: process.env.EXPO_PUBLIC_APP_ID || '',
        responseType: ResponseType.Token,
        scopes: ['public_profile', 'email'],
        redirectUri: makeRedirectUri({
            scheme: 'com.factcoding.secondcar',
            path: '/auth/facebook'
        }),
    });

    // Listen for auth response changes.
    useEffect(() => {
        console.log('Google Response changed, current type:', response?.type);
        if (response?.type === 'success') {
            console.log('Google Success detected in useEffect');
            handleAuthResponse(response);
        } else if (response?.type === 'error') {
            console.error('Google Auth Response Error:', response.error);
            Alert.alert(
                'Authentication Error',
                response.error?.message || 'Failed to authenticate with Google'
            );
        }
    }, [response]);

    // Listen for Facebook auth response changes.
    useEffect(() => {
        console.log('Facebook Response changed, current type:', facebookResponse?.type);
        if (facebookResponse?.type === 'success') {
            console.log('Facebook Success detected in useEffect');
            handleFacebookAuthResponse(facebookResponse);
        } else if (facebookResponse?.type === 'error') {
            console.error('Facebook Auth Response Error:', facebookResponse.error);
            Alert.alert(
                'Authentication Error',
                facebookResponse.error?.message || 'Failed to authenticate with Facebook'
            );
        }
    }, [facebookResponse]);

    // Add useEffect to monitor phoneRequired state changes
    useEffect(() => {
        console.log('Phone required state changed:', phoneRequired);
    }, [phoneRequired]);

    // Update registerUserFlow function to handle phone requirement better
    async function registerUserFlow(uid: any) {
        console.log("Starting register user flow:", uid);
        try {
            const result = await dispatch(registerWithGoogle(uid)).unwrap();
            console.log("Register user flow result:", result);

            // Check explicitly for new user status
            if (result.user?.status === "0" || !result.authorisation?.token) {
                console.log("New user detected, phone verification required");
                setPendingUid(uid);
                setPhoneRequired(true);  // Set this before returning
                return null;
            }

            if (result.status && result.authorisation?.token) {
                console.log("Existing user, storing auth data");
                await AsyncStorage.setItem('token', result.authorisation.token);
                await AsyncStorage.setItem('user', JSON.stringify(result.user));
                return result;
            }

            return null;
        } catch (error: any) {
            console.log("Register user flow error:", error);
            if (error.requiresPhone) {
                console.log("Phone verification required from error");
                setPendingUid(uid);
                setPhoneRequired(true);  // Set this before returning
                return null;
            }
            throw error;
        }
    }

    // Update handleAuthResponse to better handle state changes
    const handleAuthResponse = async (response: any) => {
        try {
            setLoading(true);
            console.log('Processing auth response:', {
                type: response.type,
                hasIdToken: !!response.params?.id_token,
                hasCode: !!response.params?.code
            });

            let idToken = null;
            let accessToken = null;

            // If tokens are returned directly (web)
            if (response.params.id_token) {
                idToken = response.params.id_token;
                accessToken = response.params.access_token;
                console.log('Tokens received directly from response');
            }
            // Otherwise, exchange the authorization code for tokens.
            else if (response.params.code) {
                console.log('Authorization code received, exchanging for tokens');
                try {
                    const tokenResult = await exchangeCodeAsync(
                        {
                            code: response.params.code,
                            clientId: Platform.select({
                                ios: process.env.EXPO_PUBLIC_IOS_CLIENT_ID,
                                android: process.env.EXPO_PUBLIC_ANDROID_CLIENT_ID,
                                default: process.env.EXPO_PUBLIC_WEB_CLIENT_ID,
                            }) || '',
                            redirectUri: makeRedirectUri({
                                scheme: 'com.factcoding.secondcar',
                                path: ''
                            }),
                            extraParams: {
                                // Include the generated code verifier from the auth request
                                code_verifier: request?.codeVerifier || "",
                            },
                        },
                        {
                            tokenEndpoint: 'https://oauth2.googleapis.com/token',
                        }
                    );
                    idToken = tokenResult.idToken;
                    accessToken = tokenResult.accessToken;
                    console.log('Token exchange successful');
                } catch (exchangeError) {
                    console.error('Token Exchange Error:', exchangeError);
                    console.log('Attempting manual token exchange as fallback...');
                    const manualExchangeResponse = await fetch('https://oauth2.googleapis.com/token', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },

                    });
                    const tokenData = await manualExchangeResponse.json();
                    console.log('Manual token exchange result:', JSON.stringify(tokenData, null, 2));
                    if (tokenData.id_token) {
                        idToken = tokenData.id_token;
                        accessToken = tokenData.access_token;
                    } else {
                        throw new Error('Failed to obtain tokens from authorization code');
                    }
                }
            }

            if (!idToken) {
                throw new Error('No ID token received from Google authentication');
            }

            // Sign in to Firebase using the received tokens.
            const credential = GoogleAuthProvider.credential(idToken, accessToken);
            const userCredential = await signInWithCredential(auth, credential);
            const user = userCredential.user;
            console.log('Firebase sign-in successful, UID:', user.uid);

            // Register with backend using Redux action
            const registerResult = await registerUserFlow(user.uid);
            console.log('Registration result:', {
                success: !!registerResult?.status,
                hasToken: !!registerResult?.authorisation?.token,
                phoneRequired
            });

            if (registerResult?.status && registerResult.authorisation?.token) {
                console.log('Complete registration, navigating to tabs');
                await router.replace('/(auth)/(tabs)');
            } else if (phoneRequired) {
                console.log('Phone verification required, showing form');
                // Phone form will be shown by renderContent
            } else {
                console.log('Unexpected registration state');
            }
        } catch (error: any) {
            console.error('Authentication Process Error:', error);
            Alert.alert('Error', error.message || 'Failed to complete Google sign-in');
        } finally {
            setLoading(false);
        }
    };

    // Handle submission of the phone number.
    const handlePhoneSubmit = async () => {
        console.log("inside phone submit");
        if (!pendingUid || !phone.trim()) {
            console.log("inside if")
            Alert.alert('Input Error', 'Please enter a valid phone number.');
            return;
        }

        try {
            setLoading(true);
            console.log("Submitting phone number for UID:", pendingUid);

            const result = await dispatch(registerWithPhone({
                uid: pendingUid,
                phone: phone.trim()
            })).unwrap();

            console.log("Phone registration result:", result);

            if (result.status && result.authorisation?.token) {
                // Store auth data
                await AsyncStorage.setItem('token', result.authorisation.token);
                await AsyncStorage.setItem('user', JSON.stringify(result.user));

                setPhoneRequired(false);
                setPendingUid(null);

                // Navigate to tabs
                await router.replace('/(auth)/(tabs)');
            } else {
                throw new Error(result.message || 'Phone registration failed');
            }
        } catch (error: any) {
            console.error("Phone registration error:", error);
            Alert.alert('Error', error.message || 'Failed to register phone');
        } finally {
            setLoading(false);
        }
    };

    // Function to prompt Google sign-in.
    const handleGoogleSignIn = async () => {
        try {
            setLoading(true);
            console.log('Starting Google sign-in process...');
            if (!request) {
                console.log('Auth request not ready yet');
                Alert.alert('Error', 'Authentication not ready. Please try again.');
                setLoading(false);
                return;
            }
            console.log('Prompting for authentication...');
            const result = await promptAsync();
            console.log('Prompt result type:', result.type);
            if (result.type === 'success') {
                console.log('Handling successful authentication directly');
                await handleAuthResponse(result);
            } else {
                console.log('Authentication not successful:', result.type);
                setLoading(false);
            }
        } catch (error: any) {
            console.error('Google Sign-In Prompt Error:', error);
            Alert.alert('Error', error.message || 'Failed to open Google sign-in');
            setLoading(false);
        }
    };

    // ----- Facebook Sign-In Functions -----

    // Facebook user registration flow (using same endpoint as Google)
    async function registerFacebookUserFlow(uid: any) {
        console.log("Starting Facebook register user flow:", uid);
        try {
            // Use the same registerWithGoogle endpoint for Facebook authentication
            const result = await dispatch(registerWithGoogle(uid)).unwrap();
            console.log("Facebook register user flow result:", result);

            // Check explicitly for new user status
            if (result.user?.status === "0" || !result.authorisation?.token) {
                console.log("New Facebook user detected, phone verification required");
                setPendingUid(uid);
                setPhoneRequired(true);  // Set this before returning
                return null;
            }

            if (result.status && result.authorisation?.token) {
                console.log("Existing Facebook user, storing auth data");
                await AsyncStorage.setItem('token', result.authorisation.token);
                await AsyncStorage.setItem('user', JSON.stringify(result.user));
                return result;
            }

            return null;
        } catch (error: any) {
            console.log("Facebook register user flow error:", error);
            if (error.requiresPhone) {
                console.log("Phone verification required from Facebook error");
                setPendingUid(uid);
                setPhoneRequired(true);  // Set this before returning
                return null;
            }
            throw error;
        }
    }

    // Handle Facebook authentication response
    const handleFacebookAuthResponse = async (response: any) => {
        try {
            setLoading(true);
            console.log('Processing Facebook auth response:', {
                type: response.type,
                hasAccessToken: !!response.authentication?.accessToken
            });

            if (!response.authentication?.accessToken) {
                throw new Error('No access token received from Facebook authentication');
            }

            console.log('Facebook authentication successful, creating Firebase credential');

            // Sign in to Firebase using the received access token
            const credential = FacebookAuthProvider.credential(response.authentication.accessToken);
            const userCredential = await signInWithCredential(auth, credential);
            const user = userCredential.user;
            console.log('Facebook Firebase sign-in successful, UID:', user.uid);

            // Register with backend using Redux action
            const registerResult = await registerFacebookUserFlow(user.uid);
            console.log('Facebook registration result:', {
                success: !!registerResult?.status,
                hasToken: !!registerResult?.authorisation?.token,
                phoneRequired
            });

            if (registerResult?.status && registerResult.authorisation?.token) {
                console.log('Complete Facebook registration, navigating to tabs');
                await router.replace('/(auth)/(tabs)');
            } else if (phoneRequired) {
                console.log('Phone verification required, showing form');
                // Phone form will be shown by renderContent
            } else {
                console.log('Unexpected Facebook registration state');
            }
        } catch (error: any) {
            console.error('Facebook Authentication Process Error:', error);
            Alert.alert('Error', error.message || 'Failed to complete Facebook sign-in');
        } finally {
            setLoading(false);
        }
    };

    // Function to prompt Facebook sign-in
    const handleFacebookSignIn = async () => {
        try {
            setLoading(true);
            console.log('Starting Facebook sign-in process...');
            if (!facebookRequest) {
                console.log('Facebook auth request not ready yet');
                Alert.alert('Error', 'Facebook authentication not ready. Please try again.');
                setLoading(false);
                return;
            }
            console.log('Prompting for Facebook authentication...');
            const result = await facebookPromptAsync();
            console.log('Facebook prompt result type:', result.type);
            if (result.type === 'success') {
                console.log('Handling successful Facebook authentication directly');
                await handleFacebookAuthResponse(result);
            } else {
                console.log('Facebook authentication not successful:', result.type);
                setLoading(false);
            }
        } catch (error: any) {
            console.error('Facebook Sign-In Prompt Error:', error);
            Alert.alert('Error', error.message || 'Failed to open Facebook sign-in');
            setLoading(false);
        }
    };

    const renderContent = () => {
        console.log('Rendering social buttons with phone required:', phoneRequired);
        if (phoneRequired) {
            console.log('Rendering phone input form', { pendingUid, phone });
            return (
                <View style={[styles.socialButtonsRow, styles.phoneContainer]}>
                    <ThemedText style={styles.label}>Please enter your phone number:</ThemedText>
                    <TextInput
                        style={[styles.phoneInput, { color: '#000' }]}
                        value={phone}
                        onChangeText={setPhone}
                        placeholder="Phone Number"
                        placeholderTextColor="#666"
                        keyboardType="phone-pad"
                        autoFocus
                    />
                    <ThemedButton
                        onPress={handlePhoneSubmit}
                        disabled={loading || !phone.trim()}
                        style={styles.socialButton}
                    >
                        {loading ? (
                            <View style={styles.loadingContainer}>
                                <ActivityIndicator color="#000" style={styles.activityIndicator} />
                                <ThemedText style={styles.socialButtonText}>Submitting...</ThemedText>
                            </View>
                        ) : (
                            <ThemedText style={styles.socialButtonText}>Submit Phone</ThemedText>
                        )}
                    </ThemedButton>
                </View>
            );
        }

        // Default Google and Facebook sign-in buttons
        return (
            <View style={styles.socialButtonsRow}>
                <ThemedButton
                    style={[styles.socialButton, { backgroundColor: '#fff', borderWidth: 1, borderColor: '#ddd' }]}
                    onPress={handleGoogleSignIn}
                    disabled={loading}
                >
                    <View style={styles.socialButtonContent}>
                        <Image
                            source={require('@/assets/images/googleImage.png')}
                            style={styles.socialIcon}
                        />
                        {loading && !facebookLoading ? (
                            <View style={styles.loadingContainer}>
                                <ActivityIndicator color="#000" style={styles.activityIndicator} />
                                <ThemedText style={styles.socialButtonText}>Signing in...</ThemedText>
                            </View>
                        ) : (
                            <ThemedText style={styles.socialButtonText}>Continue with Google</ThemedText>
                        )}
                    </View>
                </ThemedButton>

                <ThemedButton
                    style={[styles.socialButton, { backgroundColor: '#1877F2' }]}
                    onPress={handleFacebookSignIn}
                    disabled={loading}
                >
                    <View style={styles.socialButtonContent}>
                        <Ionicons
                            name="logo-facebook"
                            size={20}
                            color="#fff"
                            style={styles.socialIcon}
                        />
                        {loading && facebookLoading ? (
                            <View style={styles.loadingContainer}>
                                <ActivityIndicator color="#fff" style={styles.activityIndicator} />
                                <ThemedText style={[styles.socialButtonText, { color: '#fff' }]}>Signing in...</ThemedText>
                            </View>
                        ) : (
                            <ThemedText style={[styles.socialButtonText, { color: '#fff' }]}>Continue with Facebook</ThemedText>
                        )}
                    </View>
                </ThemedButton>
            </View>
        );
    };

    // Return the rendered content
    return renderContent();
}

const styles = StyleSheet.create({
    socialButtonsRow: {
        flexDirection: 'column',
        width: '100%',
        gap: 10,
    },
    socialButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%',
        paddingVertical: 12,
        borderRadius: 8,
    },
    socialButtonContent: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 10,
    },
    socialIcon: {
        width: 24,
        height: 24,
    },
    socialButtonText: {
        fontSize: 16,
        fontWeight: '600',
    },
    loadingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    activityIndicator: {
        marginRight: 8,
    },
    phoneContainer: {
        padding: 16,
        width: '100%',
        backgroundColor: '#fff',
        borderRadius: 8,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
    },
    label: {
        fontSize: 16,
        marginBottom: 16,
        fontWeight: '600',
        textAlign: 'center',
    },
    phoneInput: {
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        padding: 12,
        marginBottom: 16,
        fontSize: 16,
        width: '100%',
        backgroundColor: '#f5f5f5',
    },
});
