import { router, Tabs } from 'expo-router';
import React from 'react';
import { Platform, TouchableOpacity, Alert } from 'react-native';
import { COLORS } from '@/constants/theme';
import { Ionicons } from '@expo/vector-icons';
import { useAppSelector } from '@/store/hooks';
import { useNavigation } from '@/contexts/NavigationContext';

interface SharedTabNavigationProps {
  isPublic?: boolean;
}

export default function SharedTabNavigation({ isPublic = false }: SharedTabNavigationProps) {
  const { isAuthenticated } = useAppSelector((state) => state.auth);
  const { setIntendedDestination } = useNavigation();

  const handleProtectedTabPress = (tabName: string, route: string) => {
    if (isPublic && !isAuthenticated) {
      // Store the intended destination
      setIntendedDestination(route);

      Alert.alert(
        'Login Required',
        `Please log in to access the ${tabName} section.`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Login',
            onPress: () => router.push('/auth/register')
          }
        ]
      );
      return false; // Prevent navigation
    }
    return true; // Allow navigation
  };

  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: "#FC6316",
        tabBarInactiveTintColor: COLORS.text.secondary,
        tabBarStyle: {
          ...Platform.select({
            ios: {
              backgroundColor: COLORS.white,
              height: 88,
              paddingBottom: 32,
              shadowColor: '#000',
              shadowOffset: { width: 0, height: -3 },
              shadowOpacity: 0.05,
              shadowRadius: 6,
              elevation: 5,
              borderTopWidth: 0,
            },
            android: {
              backgroundColor: COLORS.white,
              paddingBottom: 12,
              elevation: 8,
              borderTopWidth: 0,
            },
          }),
          borderTopColor: 'transparent',
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '600',
          marginTop: 0,
        },
        tabBarIconStyle: {
          marginBottom: -2,
        },
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: 'Home',
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons
              name="home"
              size={focused ? size + 4 : size}
              color={color}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="sale"
        options={{
          title: 'Sale',
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons
              name="key-outline"
              size={focused ? size + 4 : size}
              color={isPublic && !isAuthenticated ? COLORS.text.secondary : color}
            />
          ),
          headerShown: true,
          headerTitle: 'Sell',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: 'bold',
          },
          headerStyle: { backgroundColor: COLORS.primary },
          headerTitleAlign: 'center',
          headerTintColor: COLORS.white,
          headerLeft(props) {
            return (
              <TouchableOpacity onPress={() => router.back()}>
                <Ionicons
                  name="arrow-back"
                  size={24}
                  color="white"
                  style={{ marginLeft: 16 }}
                />
              </TouchableOpacity>
            );
          },
        }}
        listeners={isPublic ? {
          tabPress: (e) => {
            if (!handleProtectedTabPress('Sale', '/(auth)/(tabs)/sale')) {
              e.preventDefault();
            }
          },
        } : undefined}
      />
      <Tabs.Screen
        name="buy"
        options={{
          title: 'Buy',
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons
              name="car-outline"
              size={focused ? size + 4 : size}
              color={color}
            />
          ),
          headerShown: true,
          headerTitle: 'Buy',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: 'bold',
          },
          headerStyle: { backgroundColor: COLORS.primary },
          headerTitleAlign: 'center',
          headerTintColor: COLORS.white,
          headerLeft(props) {
            return (
              <TouchableOpacity onPress={() => router.back()}>
                <Ionicons
                  name="arrow-back"
                  size={24}
                  color="white"
                  style={{ marginLeft: 16 }}
                />
              </TouchableOpacity>
            );
          },
        }}
      />

      <Tabs.Screen
        name="account"
        options={{
          title: 'Account',
          tabBarIcon: ({ color, size, focused }) => (
            <Ionicons
              name="person-outline"
              size={focused ? size + 4 : size}
              color={isPublic && !isAuthenticated ? COLORS.text.secondary : color}
            />
          ),
          headerShown: true,
          headerTitle: 'Account',
          headerTitleStyle: {
            fontSize: 20,
            fontWeight: 'bold',
          },
          headerStyle: { backgroundColor: COLORS.primary },
          headerTitleAlign: 'center',
          headerTintColor: COLORS.white,
          headerLeft(props) {
            return (
              <TouchableOpacity onPress={() => router.back()}>
                <Ionicons
                  name="arrow-back"
                  size={24}
                  color="white"
                  style={{ marginLeft: 16 }}
                />
              </TouchableOpacity>
            );
          },
        }}
        listeners={isPublic ? {
          tabPress: (e) => {
            if (!handleProtectedTabPress('Account', '/(auth)/(tabs)/account')) {
              e.preventDefault();
            }
          },
        } : undefined}
      />
    </Tabs>
  );
}
