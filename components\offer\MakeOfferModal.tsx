import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Modal,
  TouchableOpacity,
  TouchableWithoutFeedback,
  TextInput,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Animated,
  Keyboard,
  Platform,
  KeyboardAvoidingView,
  TextStyle,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { Ionicons } from '@expo/vector-icons';
import { ThemedText } from '@/components/common/ThemedText';
import { COLORS, SPACING, BORDERS, FONTS, SHADOWS } from '@/constants/theme';
import { AppDispatch, RootState } from '@/store/store';
import { makeOffer, clearOfferState } from '@/store/slices/offerSlice';

interface MakeOfferModalProps {
  visible: boolean;
  onClose: () => void;
  vehicleId: number;
  vehicleType: string;
  vehiclePrice: number;
  onOfferSuccess?: () => void;
}

export default function MakeOfferModal({
  visible,
  onClose,
  vehicleId,
  vehicleType,
  vehiclePrice,
  onOfferSuccess,
}: MakeOfferModalProps) {
  const dispatch = useDispatch<AppDispatch>();
  const { submitting, submitSuccess, error } = useSelector(
    (state: RootState) => state.offer
  );

  const [offerAmount, setOfferAmount] = useState('');
  const [slideAnim] = useState(new Animated.Value(300));
  const [backdropOpacity] = useState(new Animated.Value(0));
  const [contentOpacity] = useState(new Animated.Value(0));
  const [scaleAnim] = useState(new Animated.Value(0.95));
  const [isAnimating, setIsAnimating] = useState(false);
  const [modalState, setModalState] = useState<'hidden' | 'opening' | 'visible' | 'closing'>('hidden');

  // Refs for managing component lifecycle
  const alertShown = useRef(false);
  const inputRef = useRef<TextInput>(null);
  const animationCompleted = useRef(false);
  const isClosing = useRef(false);
  const hasHandledSuccess = useRef(false); // New ref to track if success was already handled
  const hasHandledError = useRef(false); // New ref to track if error was already handled

  // Calculate minimum offer (20% of vehicle price)
  const minimumOffer = Math.ceil(vehiclePrice * 0.2);
  const offerRange = `₹ ${minimumOffer.toLocaleString('en-IN')} - ₹ ${vehiclePrice.toLocaleString('en-IN')}`;

  // Reset state when modal opens - only trigger when actually opening
  useEffect(() => {
    if (visible && modalState === 'hidden' && !isClosing.current) {
      // Reset all state and refs only when truly opening for the first time
      setOfferAmount('');
      dispatch(clearOfferState());
      alertShown.current = false;
      animationCompleted.current = false;
      isClosing.current = false;
      hasHandledSuccess.current = false;
      hasHandledError.current = false;
      openModal();
    } else if (!visible && modalState !== 'hidden' && modalState !== 'closing') {
      closeModal();
    }
  }, [visible, modalState]);

  // Handle success state - show alert and let parent handle modal closure
  useEffect(() => {
    if (submitSuccess &&
      !hasHandledSuccess.current &&
      modalState === 'visible' &&
      !isClosing.current &&
      !alertShown.current) {

      hasHandledSuccess.current = true;
      alertShown.current = true;
      showSuccessAlert();
    }
  }, [submitSuccess]);

  // Handle error state - prevent multiple triggers
  useEffect(() => {
    if (error &&
      !hasHandledError.current &&
      modalState === 'visible' &&
      !isClosing.current &&
      !alertShown.current) {

      hasHandledError.current = true;
      alertShown.current = true;
      showErrorAlert();
    }
  }, [error]);

  const openModal = () => {
    if (isClosing.current || modalState !== 'hidden') return;

    setModalState('opening');
    setIsAnimating(true);

    // Reset animation values
    slideAnim.setValue(300);
    backdropOpacity.setValue(0);
    contentOpacity.setValue(0);
    scaleAnim.setValue(0.95);

    // Staggered animation for smooth opening
    Animated.sequence([
      Animated.timing(backdropOpacity, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.parallel([
        Animated.spring(slideAnim, {
          toValue: 0,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
        Animated.timing(contentOpacity, {
          toValue: 1,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
      ]),
    ]).start(() => {
      if (!isClosing.current) {
        setIsAnimating(false);
        setModalState('visible');
        animationCompleted.current = true;
        // Auto-focus input after animation completes
        setTimeout(() => {
          if (!isClosing.current) {
            inputRef.current?.focus();
          }
        }, 100);
      }
    });
  };

  const closeModal = () => {
    if (modalState === 'closing' || modalState === 'hidden' || isClosing.current) return;

    isClosing.current = true;
    setModalState('closing');
    setIsAnimating(true);

    // Dismiss keyboard first
    Keyboard.dismiss();

    // Smooth closing animation
    Animated.parallel([
      Animated.timing(contentOpacity, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 300,
        duration: 250,
        useNativeDriver: true,
      }),
      Animated.timing(backdropOpacity, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start(() => {
      // Reset all state after animation completes
      setIsAnimating(false);
      setModalState('hidden');
      alertShown.current = false;
      isClosing.current = false;
      hasHandledSuccess.current = false;
      hasHandledError.current = false;
      animationCompleted.current = false;

      // Clear Redux state
      dispatch(clearOfferState());

      // Call onClose to update parent component state
      onClose();
    });
  };



  const showSuccessAlert = () => {
    Alert.alert(
      'Success',
      'Your offer has been submitted successfully!',
      [
        {
          text: 'OK',
          onPress: () => {
            // Clear Redux state first
            dispatch(clearOfferState());

            // Call success callback to update parent
            if (onOfferSuccess) {
              onOfferSuccess();
            }

            // Close modal through parent component
            onClose();
          },
        },
      ],
      {
        cancelable: false,
        onDismiss: () => {
          // Clear Redux state first
          dispatch(clearOfferState());

          // Call success callback to update parent
          if (onOfferSuccess) {
            onOfferSuccess();
          }

          // Close modal through parent component
          onClose();
        }
      }
    );
  };

  const showErrorAlert = () => {
    Alert.alert(
      'Error',
      error || 'Something went wrong. Please try again.',
      [
        {
          text: 'OK',
          onPress: () => {
            // Reset error handling flags but don't close modal - allow retry
            alertShown.current = false;
            hasHandledError.current = false;
            isClosing.current = false;
            // Clear only the error state, keep modal open for retry
            dispatch(clearOfferState());
          },
        },
      ],
      {
        cancelable: false,
        onDismiss: () => {
          // Reset error handling flags but don't close modal - allow retry
          alertShown.current = false;
          hasHandledError.current = false;
          isClosing.current = false;
          // Clear only the error state, keep modal open for retry
          dispatch(clearOfferState());
        }
      }
    );
  };

  const showValidationAlert = (title: string, message: string): Promise<void> => {
    return new Promise((resolve) => {
      Alert.alert(
        title,
        message,
        [
          {
            text: 'OK',
            onPress: () => {
              resolve();
            },
          },
        ],
        { cancelable: false }
      );
    });
  };

  const validateOffer = async (amount: string): Promise<boolean> => {
    const numericAmount = parseInt(amount.replace(/,/g, ''));

    if (isNaN(numericAmount) || numericAmount <= 0) {
      await showValidationAlert(
        'Invalid Amount',
        'Please enter a valid offer amount.'
      );
      return false;
    }

    if (numericAmount < minimumOffer) {
      await showValidationAlert(
        'Minimum Offer Required',
        `The minimum offer amount is ₹ ${minimumOffer.toLocaleString('en-IN')} (20% of the vehicle price).`
      );
      return false;
    }

    if (numericAmount > vehiclePrice) {
      await showValidationAlert(
        'Offer Too High',
        `Your offer cannot exceed the vehicle price of ₹ ${vehiclePrice.toLocaleString('en-IN')}.`
      );
      return false;
    }

    return true;
  };

  const handleSubmitOffer = async () => {
    // Prevent submission during animations or if already processing
    if (isAnimating ||
      submitting ||
      alertShown.current ||
      modalState !== 'visible' ||
      isClosing.current ||
      hasHandledSuccess.current ||
      hasHandledError.current) {
      return;
    }

    const isValid = await validateOffer(offerAmount);
    if (!isValid) {
      return;
    }

    const numericAmount = parseInt(offerAmount.replace(/,/g, ''));

    dispatch(makeOffer({
      id: vehicleId,
      type: vehicleType,
      make_offer: numericAmount,
    }));
  };

  const formatAmount = (text: string) => {
    // Remove non-numeric characters
    const numericValue = text.replace(/[^0-9]/g, '');

    // Format with commas
    if (numericValue) {
      const number = parseInt(numericValue);
      // Prevent extremely large numbers
      if (number <= 99999999) {
        const formatted = number.toLocaleString('en-IN');
        setOfferAmount(formatted);
      }
    } else {
      setOfferAmount('');
    }
  };

  const isValidOffer = () => {
    if (!offerAmount) return false;
    const numericAmount = parseInt(offerAmount.replace(/,/g, ''));
    return !isNaN(numericAmount) &&
      numericAmount >= minimumOffer &&
      numericAmount <= vehiclePrice;
  };

  const handleBackdropPress = () => {
    // Only close if not animating and not submitting
    if (!isAnimating &&
      !submitting &&
      modalState === 'visible' &&
      !isClosing.current &&
      !alertShown.current) {
      closeModal();
    }
  };

  // Don't render modal if it's hidden
  if (modalState === 'hidden') {
    return null;
  }

  return (
    <Modal
      visible={true}
      transparent={true}
      animationType="none"
      onRequestClose={closeModal}
      statusBarTranslucent={true}
      hardwareAccelerated={true}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}
      >
        <TouchableWithoutFeedback onPress={handleBackdropPress}>
          <Animated.View
            style={[styles.backdrop, { opacity: backdropOpacity }]}
          />
        </TouchableWithoutFeedback>

        <Animated.View
          style={[
            styles.modalContent,
            {
              transform: [
                { translateY: slideAnim },
                { scale: scaleAnim }
              ],
              opacity: contentOpacity,
            },
          ]}
        >
          <View style={styles.header}>
            <ThemedText style={styles.title}>Make Offer</ThemedText>
            <TouchableOpacity
              onPress={closeModal}
              disabled={isAnimating || submitting}
              style={[
                styles.closeButton,
                (isAnimating || submitting) && styles.closeButtonDisabled
              ]}
            >
              <Ionicons
                name="close"
                size={24}
                color={isAnimating || submitting ? COLORS.gray[400] : COLORS.text.secondary}
              />
            </TouchableOpacity>
          </View>

          <ThemedText style={styles.rangeText}>
            Offer Range: {offerRange}
          </ThemedText>

          <View style={styles.inputContainer}>
            <ThemedText style={styles.inputLabel}>Enter Offer Amount</ThemedText>
            <View style={[
              styles.inputWrapper,
              !isValidOffer() && offerAmount && styles.inputWrapperError
            ]}>
              <ThemedText style={styles.currencySymbol}>₹</ThemedText>
              <TextInput
                ref={inputRef}
                style={styles.input}
                value={offerAmount}
                onChangeText={formatAmount}
                placeholder="Enter Amount"
                placeholderTextColor={COLORS.text.secondary}
                keyboardType="numeric"
                maxLength={15}
                editable={!submitting && modalState === 'visible'}
                returnKeyType="done"
                onSubmitEditing={handleSubmitOffer}
              />
            </View>
            {!isValidOffer() && offerAmount && (
              <ThemedText style={styles.errorText}>
                Amount must be between ₹ {minimumOffer.toLocaleString('en-IN')} and ₹ {vehiclePrice.toLocaleString('en-IN')}
              </ThemedText>
            )}
          </View>

          <TouchableOpacity
            style={[
              styles.submitButton,
              (!isValidOffer() || submitting || isAnimating) && styles.submitButtonDisabled,
            ]}
            onPress={handleSubmitOffer}
            disabled={!isValidOffer() || submitting || isAnimating}
            activeOpacity={0.8}
          >
            {submitting ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" color={COLORS.white} />
                <ThemedText style={[styles.submitButtonText, styles.loadingText]}>
                  Submitting...
                </ThemedText>
              </View>
            ) : (
              <ThemedText style={styles.submitButtonText}>Continue</ThemedText>
            )}
          </TouchableOpacity>
        </Animated.View>
      </KeyboardAvoidingView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: COLORS.white,
    borderTopLeftRadius: BORDERS.radius.lg,
    borderTopRightRadius: BORDERS.radius.lg,
    padding: SPACING.lg,
    paddingBottom: SPACING.xl,
    minHeight: 300,
    ...SHADOWS.md,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  title: {
    fontSize: FONTS.size.h2,
    fontWeight: FONTS.weight.bold,
    color: COLORS.text.primary,
    flex: 1,
    textAlign: 'center',
  } as TextStyle,
  closeButton: {
    padding: SPACING.xs,
    borderRadius: 20,
    position: 'absolute',
    right: 0,
    top: 0,
  },
  closeButtonDisabled: {
    opacity: 0.5,
  },
  rangeText: {
    fontSize: FONTS.size.caption,
    color: COLORS.text.secondary,
    textAlign: 'center',
    marginBottom: SPACING.lg,
  },
  inputContainer: {
    marginBottom: SPACING.xl,
  },
  inputLabel: {
    fontSize: FONTS.size.caption,
    color: COLORS.text.primary,
    marginBottom: SPACING.xs,
    fontWeight: FONTS.weight.medium,
  } as TextStyle,
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: BORDERS.width.thin,
    borderColor: COLORS.border.primary,
    borderRadius: BORDERS.radius.sm,
    backgroundColor: COLORS.white,
    paddingHorizontal: SPACING.md,
    minHeight: 56,
  },
  inputWrapperError: {
    borderColor: COLORS.error || '#FF6B6B',
    borderWidth: 2,
  },
  currencySymbol: {
    fontSize: FONTS.size.body,
    color: COLORS.text.primary,
    marginRight: SPACING.xs,
    fontWeight: FONTS.weight.medium,
  } as TextStyle,
  input: {
    flex: 1,
    fontSize: FONTS.size.body,
    color: COLORS.text.primary,
    paddingVertical: SPACING.md,
  },
  errorText: {
    fontSize: FONTS.size.small || 12,
    color: COLORS.error || '#FF6B6B',
    marginTop: SPACING.xs,
  },
  submitButton: {
    backgroundColor: COLORS.primary,
    borderRadius: BORDERS.radius.sm,
    paddingVertical: SPACING.md,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 56,
    ...SHADOWS.sm,
  },
  submitButtonDisabled: {
    backgroundColor: COLORS.gray[400],
    opacity: 0.6,
  },
  submitButtonText: {
    color: COLORS.white,
    fontSize: FONTS.size.body,
    fontWeight: FONTS.weight.bold,
  } as TextStyle,
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingText: {
    marginLeft: SPACING.sm,
  },
});