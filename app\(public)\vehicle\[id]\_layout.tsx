import { COLORS } from '@/constants/theme';
import { Ionicons } from '@expo/vector-icons';
import { router, Stack } from 'expo-router';
import { Text, TouchableOpacity } from 'react-native';

export default function VehicleLayout() {
    return (
        <Stack
            screenOptions={{
                headerShown: true,
                presentation: 'modal',
                animationTypeForReplace: 'push',
                animation: 'slide_from_right',
                headerTitle: 'Vehicle Details',
                headerStyle: { backgroundColor: COLORS.primary },
                headerTitleAlign: 'center',
                headerTintColor: COLORS.white,
                headerLeft(props) {
                    return (
                        <TouchableOpacity onPress={() => router.back()}>
                            <Ionicons
                                name="arrow-back"
                                size={24}
                                color="white"
                                style={{ marginLeft: 10 }}
                            />
                        </TouchableOpacity>
                    );
                },
            }}
        />
    );
}