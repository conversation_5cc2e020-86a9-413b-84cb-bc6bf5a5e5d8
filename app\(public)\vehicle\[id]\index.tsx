import React, { useEffect, useState, useRef } from 'react';
import {
    View,
    ScrollView,
    Image,
    StyleSheet,
    TouchableOpacity,
    Dimensions,
    Linking,
    Alert,
    Platform,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useLocalSearchParams, router } from 'expo-router';
import { AppDispatch, RootState } from '@/store/store';
import { fetchVehicleDetail } from '@/store/slices/vehicleDetailSlice';
import { ThemedText } from '@/components/common/ThemedText';
import { COLORS, SPACING } from '@/constants/theme';
import LoadingIndicator from '@/components/common/LoadingIndicator';
import { Ionicons } from '@expo/vector-icons';
import { VehicleType } from '@/store/slices/vehicleSlice';
import Constants from 'expo-constants';
import * as Clipboard from 'expo-clipboard';
import { INSURANCE_OPTIONS } from '@/constants/commercialVehicle';
import MakeOfferModal from '@/components/offer/MakeOfferModal';
import OffersList from '@/components/offer/OffersList';
import { resetOffers } from '@/store/slices/offerSlice';
import { useAppSelector } from '@/store/hooks';
import { useNavigation } from '@/contexts/NavigationContext';
import { captureRef } from 'react-native-view-shot';
import * as Sharing from 'expo-sharing';
import Share from 'react-native-share';
import * as FileSystem from 'expo-file-system';

const { width } = Dimensions.get('window');

// Helper function to get insurance type display name
const getInsuranceDisplayName = (insuranceType: string) => {
    const option = INSURANCE_OPTIONS.find(opt => opt.key === insuranceType);
    return option ? option.name : insuranceType;
};

export default function VehicleDetailScreen() {
    const { id, type } = useLocalSearchParams<{ id: string; type: string }>();
    const dispatch = useDispatch<AppDispatch>();
    const { vehicle, loading, error, offers } = useSelector(
        (state: RootState) => state.vehicleDetail
    );
    const { selectedVehicleType } = useSelector((state: RootState) => state.vehicles);
    const { submitSuccess } = useSelector((state: RootState) => state.offer);
    const { isAuthenticated } = useAppSelector((state) => state.auth);
    const { setIntendedDestination } = useNavigation();
    const [selectedImage, setSelectedImage] = useState<string>('');
    const [isOverviewExpanded, setIsOverviewExpanded] = useState(true);
    const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(true);
    const [makeOfferModalVisible, setMakeOfferModalVisible] = useState(false);
    const [isCapturingShare, setIsCapturingShare] = useState(false);
    const shareableViewRef = useRef<View>(null);
    const overlayShareRef = useRef<View>(null);

    useEffect(() => {
        if (id && type) {
            dispatch(fetchVehicleDetail({ id: parseInt(id), type: type as VehicleType }));
        }
    }, [id, type, dispatch]);

    useEffect(() => {
        if (vehicle?.primary_image) {
            setSelectedImage(vehicle.primary_image);
        }
    }, [vehicle]);

    // Reset offers when component unmounts
    useEffect(() => {
        return () => {
            dispatch(resetOffers());
        };
    }, [dispatch]);

    const handleChatPress = () => {
        if (!vehicle?.user?.phone) {
            Alert.alert('Error', 'Phone number not available');
            return;
        }

        // Clean phone number (remove any non-numeric characters)
        const cleanPhoneNumber = vehicle.user.phone.replace(/\D/g, '');

        // Make sure we have a valid phone number
        if (!cleanPhoneNumber || cleanPhoneNumber.length < 10) {
            Alert.alert('Error', 'Invalid phone number');
            return;
        }

        const whatsappMessage = `Hi, I am interested in your ${vehicle?.title} listed on 2ndCar. Is it still available?`;
        const whatsappUrl = `whatsapp://send?phone=91${cleanPhoneNumber}&text=${encodeURIComponent(whatsappMessage)}`;

        Linking.canOpenURL(whatsappUrl)
            .then(supported => {
                if (supported) {
                    return Linking.openURL(whatsappUrl);
                } else {
                    // Try web URL as fallback
                    const webWhatsappUrl = `https://wa.me/91${cleanPhoneNumber}?text=${encodeURIComponent(whatsappMessage)}`;
                    return Linking.openURL(webWhatsappUrl)
                        .catch(() => {
                            throw new Error('WhatsApp is not installed on your device');
                        });
                }
            })
            .catch(error => {
                console.error('Error opening WhatsApp:', error);
                Alert.alert(
                    'Error',
                    'Could not open WhatsApp. Please make sure WhatsApp is installed on your device.',
                    [{ text: 'OK' }]
                );
            });
    };

    const handleCallPress = () => {
        const phoneNumber = vehicle?.user?.phone?.trim();

        if (!phoneNumber) {
            Alert.alert('Error', 'Phone number not available');
            return;
        }

        // iOS simulator check using Expo Constants
        if (Platform.OS === 'ios' && !Constants.isDevice) {
            Alert.alert('Error', 'Phone calls are not supported on the iOS simulator.');
            return;
        }

        // Clean the phone number:
        // Preserve the leading '+' if it exists, else remove all non-numeric characters.
        const cleanedPhoneNumber = phoneNumber.startsWith('+')
            ? '+' + phoneNumber.slice(1).replace(/\D/g, '')
            : phoneNumber.replace(/\D/g, '');

        // Validate: Exclude '+' from the digit count
        const numericPhoneNumber = cleanedPhoneNumber.startsWith('+')
            ? cleanedPhoneNumber.slice(1)
            : cleanedPhoneNumber;
        if (numericPhoneNumber.length < 10) {
            Alert.alert('Error', 'Invalid phone number');
            return;
        }

        // Choose URL scheme based on platform:
        // On iOS, 'telprompt:' shows a prompt; on Android, use 'tel:'.
        const urlScheme = Platform.OS === 'ios' ? 'telprompt:' : 'tel:';
        const phoneUrl = `${urlScheme}${cleanedPhoneNumber}`;

        // Attempt to open the URL directly and handle errors.
        Linking.openURL(phoneUrl).catch((error) => {
            console.error('Failed to open URL:', error);
            Alert.alert(
                'Error',
                'Could not initiate the phone call. Your device may not support this feature.',
                [{ text: 'OK' }]
            );
        });
    };

    const handleSharePress = async () => {
        if (!vehicle) {
            Alert.alert('Error', 'Vehicle details not available');
            return;
        }

        try {
            setIsCapturingShare(true);

            // Wait for the overlay to render properly
            await new Promise(resolve => setTimeout(resolve, 1500));

            if (!overlayShareRef.current) {
                setIsCapturingShare(false);
                throw new Error('Unable to generate share image.');
            }

            // Capture with higher quality and better settings
            const uri = await captureRef(overlayShareRef, {
                format: 'png',
                quality: 1.0, // Maximum quality
                result: 'tmpfile',
                width: 800, // Fixed width for consistency
                height: 1000, // Fixed height for proper aspect ratio
            });

            setIsCapturingShare(false);
            console.log('Image captured:', uri);

            const deepLink = `https://car.2ndcar.in/api/vehicle/${selectedVehicleType}/${vehicle.id}`;
            const shareMessage = `Check out this ${vehicle?.title} on 2ndCar!\n\nPrice: ₹${parseInt(vehicle.price).toLocaleString('en-IN')}\nYear: ${vehicle.year} | KMs: ${vehicle.kilometers_driven}\nLocation: ${vehicle.location}\n\nView more details: ${deepLink}`;

            const shareOptions = {
                title: `${vehicle.title} - 2ndCar`,
                message: shareMessage,
                url: 'file://' + uri,
                type: 'image/png',
                subject: `${vehicle.title} - Available on 2ndCar`,
            };

            const result = await Share.open(shareOptions);
            console.log('Shared successfully:', result);
        } catch (error) {
            setIsCapturingShare(false);
            console.error('Share error:', error);
            Alert.alert(
                'Share Failed',
                'Unable to share vehicle details. Please try again.',
                [{ text: 'OK' }]
            );
        }
    };

    const handleMakeOffer = () => {
        if (!isAuthenticated) {
            // Set intended destination and show login prompt
            setIntendedDestination(`/(auth)/vehicle/${id}?type=${type}`);
            Alert.alert(
                'Login Required',
                'Please log in to make an offer on this vehicle.',
                [
                    { text: 'Cancel', style: 'cancel' },
                    {
                        text: 'Login',
                        onPress: () => router.push('/auth/register')
                    }
                ]
            );
            return;
        }
        setMakeOfferModalVisible(true);
    };

    const handleOfferSuccess = () => {
        // Close the modal immediately when offer is successful
        setMakeOfferModalVisible(false);

        // Re-fetch vehicle details to get the updated offers
        if (id && type) {
            dispatch(fetchVehicleDetail({ id: parseInt(id), type: type as VehicleType }));
        }
    };

    // Clear offer success state when modal is closed to prevent unwanted effects
    useEffect(() => {
        if (!makeOfferModalVisible && submitSuccess) {
            // Clear the success state to prevent any side effects
            dispatch(resetOffers());
        }
    }, [makeOfferModalVisible, submitSuccess, dispatch]);

    if (loading) {
        return (
            <View style={styles.loadingContainer}>
                <LoadingIndicator size={120} color1="#ff6b6b" color2="#4ecdc4" />
            </View>
        );
    }

    if (error || !vehicle)
        return <ThemedText>Error: {error}</ThemedText>;

    // Shareable Vehicle Card Component
    const ShareableVehicleCard = () => (
        <View style={shareableCardStyles.container}>
            {/* Header with Logo and Price */}
            <View style={shareableCardStyles.header}>
                <View style={shareableCardStyles.logoContainer}>
                    <ThemedText style={shareableCardStyles.logoText}>2ndCar</ThemedText>
                    <ThemedText style={shareableCardStyles.logoSubtext}>Buy & Sell Used Vehicles</ThemedText>
                </View>
                <View style={shareableCardStyles.priceContainer}>
                    <ThemedText style={shareableCardStyles.priceLabel}>Price</ThemedText>
                    <ThemedText style={shareableCardStyles.price}>
                        ₹{parseInt(vehicle.price).toLocaleString('en-IN')}
                    </ThemedText>
                </View>
            </View>

            {/* Vehicle Image with Aspect Ratio Container */}
            <View style={shareableCardStyles.imageContainer}>
                <Image
                    source={{
                        uri: vehicle?.primary_image || 'https://placeholder.com/400x300',
                    }}
                    style={shareableCardStyles.vehicleImage}
                    resizeMode="contain"
                />
                {/* Image Overlay with Vehicle Title */}
                <View style={shareableCardStyles.imageOverlay}>
                    <ThemedText style={shareableCardStyles.vehicleTitle} numberOfLines={2}>
                        {vehicle.title}
                    </ThemedText>
                </View>
            </View>

            {/* Main Details Section */}
            <View style={shareableCardStyles.detailsContainer}>
                {/* Key Specifications Grid */}
                <View style={shareableCardStyles.specsGrid}>
                    <View style={shareableCardStyles.specItem}>
                        <Ionicons name="calendar-outline" size={20} color={COLORS.primary} />
                        <View style={shareableCardStyles.specContent}>
                            <ThemedText style={shareableCardStyles.specLabel}>Year</ThemedText>
                            <ThemedText style={shareableCardStyles.specValue}>{vehicle.year}</ThemedText>
                        </View>
                    </View>
                    <View style={shareableCardStyles.specItem}>
                        <Ionicons name="speedometer-outline" size={20} color={COLORS.primary} />
                        <View style={shareableCardStyles.specContent}>
                            <ThemedText style={shareableCardStyles.specLabel}>KMs</ThemedText>
                            <ThemedText style={shareableCardStyles.specValue}>{vehicle.kilometers_driven}</ThemedText>
                        </View>
                    </View>
                    <View style={shareableCardStyles.specItem}>
                        <Ionicons name="car-outline" size={20} color={COLORS.primary} />
                        <View style={shareableCardStyles.specContent}>
                            <ThemedText style={shareableCardStyles.specLabel}>Fuel</ThemedText>
                            <ThemedText style={shareableCardStyles.specValue}>{vehicle.fuel_type}</ThemedText>
                        </View>
                    </View>
                    <View style={shareableCardStyles.specItem}>
                        <Ionicons name="person-outline" size={20} color={COLORS.primary} />
                        <View style={shareableCardStyles.specContent}>
                            <ThemedText style={shareableCardStyles.specLabel}>Owner</ThemedText>
                            <ThemedText style={shareableCardStyles.specValue}>{vehicle.ownership}</ThemedText>
                        </View>
                    </View>
                </View>

                {/* Vehicle Information */}
                <View style={shareableCardStyles.infoSection}>
                    <View style={shareableCardStyles.infoRow}>
                        <ThemedText style={shareableCardStyles.infoLabel}>Brand & Model:</ThemedText>
                        <ThemedText style={shareableCardStyles.infoValue}>
                            {vehicle.brand} {vehicle.model}
                        </ThemedText>
                    </View>
                    {vehicle.variant && (
                        <View style={shareableCardStyles.infoRow}>
                            <ThemedText style={shareableCardStyles.infoLabel}>Variant:</ThemedText>
                            <ThemedText style={shareableCardStyles.infoValue}>{vehicle.variant}</ThemedText>
                        </View>
                    )}
                    <View style={shareableCardStyles.infoRow}>
                        <Ionicons name="location-outline" size={16} color={COLORS.text.secondary} />
                        <ThemedText style={shareableCardStyles.infoValue}>{vehicle.location}</ThemedText>
                    </View>
                    {vehicle?.insurance_type && (
                        <View style={shareableCardStyles.infoRow}>
                            <Ionicons name="shield-checkmark-outline" size={16} color={COLORS.success} />
                            <ThemedText style={shareableCardStyles.infoValue}>
                                {getInsuranceDisplayName(vehicle.insurance_type)} Insurance
                            </ThemedText>
                        </View>
                    )}
                </View>

                {/* Call to Action Footer */}
                <View style={shareableCardStyles.footer}>
                    <View style={shareableCardStyles.ctaContainer}>
                        <ThemedText style={shareableCardStyles.ctaText}>Interested? Contact Now!</ThemedText>
                        <ThemedText style={shareableCardStyles.websiteText}>Visit 2ndCar.in for more vehicles</ThemedText>
                    </View>
                </View>
            </View>
        </View>
    );

    return (
        <View style={styles.container}>
            {/* Hidden Shareable View for Capture */}
            <View style={styles.hiddenShareableContainer}>
                <View
                    ref={shareableViewRef}
                    collapsable={false}
                >
                    <ShareableVehicleCard />
                </View>
            </View>

            {/* Temporary Overlay for Share Capture */}
            {isCapturingShare && (
                <View style={styles.shareOverlay}>
                    <View style={styles.shareOverlayContent}>
                        <View ref={overlayShareRef} collapsable={false}>
                            <ShareableVehicleCard />
                        </View>
                        <ThemedText style={styles.shareOverlayText}>Generating image...</ThemedText>
                    </View>
                </View>
            )}

            {/* Header Overlay */}
            <View style={styles.header}>
                <View style={styles.headerActions}>
                    {/* <TouchableOpacity style={styles.iconButton}>
                        <Ionicons name="heart-outline" size={24} color={COLORS.white} />
                    </TouchableOpacity> */}
                    <TouchableOpacity style={styles.iconButton} onPress={handleSharePress}>
                        <Ionicons name="share-social-outline" size={24} color={COLORS.white} />
                    </TouchableOpacity>
                </View>
            </View>

            <ScrollView showsVerticalScrollIndicator={false}>
                {/* Main Image */}
                <Image
                    source={{
                        uri:
                            selectedImage ||
                            vehicle?.primary_image ||
                            'https://placeholder.com/300x200',
                    }}
                    style={styles.mainImage}
                />

                {/* Thumbnail Images */}
                <ScrollView
                    horizontal
                    style={styles.thumbnailContainer}
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={styles.thumbnailContentContainer}
                >
                    {vehicle.images.map((image, index) => (
                        <TouchableOpacity key={index} onPress={() => setSelectedImage(image)}>
                            <Image
                                source={{ uri: image }}
                                style={[
                                    styles.thumbnail,
                                    selectedImage === image && styles.selectedThumbnail,
                                ]}
                                resizeMode="cover"
                            />
                        </TouchableOpacity>
                    ))}
                </ScrollView>

                {/* Vehicle Info */}
                <View style={styles.infoContainer}>
                    <View style={styles.titleRow}>
                        <View style={styles.titleContainer}>
                            <ThemedText style={styles.title}>{vehicle.title}</ThemedText>
                            <ThemedText style={styles.variant}>{vehicle.variant}</ThemedText>
                        </View>
                    </View>

                    {/* Price and Actions */}
                    <View style={styles.actionContainer}>
                        <View style={styles.priceContainer}>
                            <ThemedText style={styles.price}>
                                ₹ {parseInt(vehicle.price).toLocaleString('en-IN')}
                            </ThemedText>
                        </View>
                        <TouchableOpacity style={styles.actionButton} onPress={handleChatPress}>
                            <Ionicons
                                name="chatbubble-ellipses-outline"
                                size={20}
                                color={COLORS.white}
                            />
                            <ThemedText style={styles.buttonText}>Chat</ThemedText>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.actionButton} onPress={handleCallPress}>
                            <Ionicons name="call-outline" size={20} color={COLORS.white} />
                            <ThemedText style={styles.buttonText}>Call</ThemedText>
                        </TouchableOpacity>
                    </View>

                    {/* Offers Section */}
                    <OffersList
                        offers={offers}
                        onPlaceOffer={handleMakeOffer}
                    />

                    {/* Overview Section */}
                    <View style={styles.section}>
                        <TouchableOpacity
                            style={styles.sectionHeader}
                            onPress={() => setIsOverviewExpanded(!isOverviewExpanded)}
                        >
                            <ThemedText style={styles.sectionTitle}>Vehicle Overview</ThemedText>
                            <Ionicons
                                name={isOverviewExpanded ? 'chevron-up' : 'chevron-down'}
                                size={24}
                                color={COLORS.text.secondary}
                            />
                        </TouchableOpacity>

                        {isOverviewExpanded && (
                            <View style={styles.overviewGrid}>
                                <View style={styles.overviewItem}>
                                    <Ionicons name="car-outline" size={24} color={COLORS.text.secondary} />
                                    <View style={styles.overviewItemText}>
                                        <ThemedText style={styles.overviewRow}>
                                            <ThemedText style={styles.overviewLabel}>Fuel Type: </ThemedText>
                                            <ThemedText style={styles.overviewValue}>{vehicle.fuel_type}</ThemedText>
                                        </ThemedText>
                                    </View>
                                </View>
                                <View style={styles.overviewItem}>
                                    <Ionicons name="speedometer-outline" size={24} color={COLORS.text.secondary} />
                                    <View style={styles.overviewItemText}>
                                        <ThemedText style={styles.overviewRow}>
                                            <ThemedText style={styles.overviewLabel}>KMs Driven: </ThemedText>
                                            <ThemedText style={styles.overviewValue}>{vehicle.kilometers_driven} KM</ThemedText>
                                        </ThemedText>
                                    </View>
                                </View>
                                <View style={styles.overviewItem}>
                                    <Ionicons name="settings-outline" size={24} color={COLORS.text.secondary} />
                                    <View style={styles.overviewItemText}>
                                        <ThemedText style={styles.overviewRow}>
                                            <ThemedText style={styles.overviewLabel}>Transmission: </ThemedText>
                                            <ThemedText style={styles.overviewValue}>{vehicle?.transmission}</ThemedText>
                                        </ThemedText>
                                    </View>
                                </View>
                                <View style={styles.overviewItem}>
                                    <Ionicons name="person-outline" size={24} color={COLORS.text.secondary} />
                                    <View style={styles.overviewItemText}>
                                        <ThemedText style={styles.overviewRow}>
                                            <ThemedText style={styles.overviewLabel}>No. of Owners: </ThemedText>
                                            <ThemedText style={styles.overviewValue}>{vehicle.ownership}</ThemedText>
                                        </ThemedText>
                                    </View>
                                </View>
                                <View style={styles.overviewItem}>
                                    <Ionicons name="calendar-outline" size={24} color={COLORS.text.secondary} />
                                    <View style={styles.overviewItemText}>
                                        <ThemedText style={styles.overviewRow}>
                                            <ThemedText style={styles.overviewLabel}>Year: </ThemedText>
                                            <ThemedText style={styles.overviewValue}>{vehicle?.year}</ThemedText>
                                        </ThemedText>
                                    </View>
                                </View>
                                <View style={styles.overviewItem}>
                                    <Ionicons name="location-outline" size={24} color={COLORS.text.secondary} />
                                    <View style={styles.overviewItemText}>
                                        <ThemedText style={styles.overviewRow}>
                                            <ThemedText style={styles.overviewLabel}>Location: </ThemedText>
                                            <ThemedText style={styles.overviewValue}>{vehicle?.location}</ThemedText>
                                        </ThemedText>
                                    </View>
                                </View>
                                {vehicle?.insurance_type && (
                                    <View style={styles.overviewItem}>
                                        <Ionicons name="shield-checkmark-outline" size={24} color={COLORS.text.secondary} />
                                        <View style={styles.overviewItemText}>
                                            <ThemedText style={styles.overviewRow}>
                                                <ThemedText style={styles.overviewLabel}>Insurance: </ThemedText>
                                                <ThemedText style={styles.overviewValue}>
                                                    {getInsuranceDisplayName(vehicle.insurance_type)}
                                                </ThemedText>
                                            </ThemedText>
                                        </View>
                                    </View>
                                )}
                            </View>
                        )}
                    </View>

                    {/* Description Section */}
                    <View style={styles.section}>
                        <TouchableOpacity
                            style={styles.sectionHeader}
                            onPress={() => setIsDescriptionExpanded(!isDescriptionExpanded)}
                        >
                            <ThemedText style={styles.sectionTitle}>Description</ThemedText>
                            <Ionicons
                                name={isDescriptionExpanded ? 'chevron-up' : 'chevron-down'}
                                size={24}
                                color={COLORS.text.secondary}
                            />
                        </TouchableOpacity>
                        {isDescriptionExpanded && (
                            <ThemedText style={styles.descriptionText}>
                                {vehicle?.description || 'No description available'}
                            </ThemedText>
                        )}
                    </View>
                </View>
            </ScrollView>

            {/* Make Offer Modal */}
            {vehicle && (
                <MakeOfferModal
                    visible={makeOfferModalVisible}
                    onClose={() => setMakeOfferModalVisible(false)}
                    vehicleId={vehicle.id}
                    vehicleType={type || 'car'}
                    vehiclePrice={parseInt(vehicle.price)}
                    onOfferSuccess={handleOfferSuccess}
                />
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        backgroundColor: COLORS.background,
        position: 'relative',
    },
    header: {
        position: 'absolute',
        top: SPACING.md,
        right: SPACING.md,
        zIndex: 10,
        flexDirection: 'row',
    },
    headerActions: {
        flexDirection: 'row',
        gap: SPACING.sm,
    },
    iconButton: {
        padding: SPACING.xs,
        backgroundColor: 'rgba(0,0,0,0.5)',
        borderRadius: 20,
        marginLeft: SPACING.sm,
    },
    mainImage: {
        width: width,
        height: width * 0.75,
    },
    thumbnailContainer: {
        backgroundColor: COLORS.background,
        paddingHorizontal: SPACING.sm,
        paddingVertical: SPACING.xs,
        marginVertical: SPACING.sm,
        minHeight: 80,
    },
    thumbnailContentContainer: {
        alignItems: 'center',
    },
    thumbnail: {
        width: 60,
        height: 60,
        marginRight: SPACING.xs,
        borderRadius: 4,
    },
    selectedThumbnail: {
        borderWidth: 2,
        borderColor: COLORS.primary,
    },
    infoContainer: {
        padding: SPACING.md,
    },
    titleRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: SPACING.md,
    },
    titleContainer: {
        flex: 1,
    },
    title: {
        fontSize: 20,
        fontWeight: 'bold',
        color: COLORS.text.primary,
    },
    variant: {
        color: COLORS.text.secondary,
        marginTop: SPACING.xs,
    },
    actionContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: SPACING.md,
    },
    priceContainer: {
        flex: 1,
    },
    price: {
        fontSize: 24,
        fontWeight: 'bold',
        color: COLORS.primary,
    },
    actionButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: COLORS.primary,
        padding: SPACING.sm,
        borderRadius: 8,
        marginLeft: SPACING.sm,
    },
    buttonText: {
        color: COLORS.white,
        marginLeft: SPACING.xs,
        fontSize: 16,
        fontWeight: 'bold',
    },
    bidSection: {
        marginVertical: SPACING.md,
        width: '100%',
    },
    bidSectionTitle: {
        fontSize: 16,
        marginBottom: SPACING.sm,
        color: COLORS.text.primary,
    },
    bidContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        gap: SPACING.md,
    },
    bidInfoContainer: {
        flexDirection: 'row',
        flex: 1,
        borderWidth: 0.4,
        borderRadius: 8,
        borderColor: '#1A2B50',
        overflow: 'hidden', // Ensures the bidNumberContainer respects the border radius
    },
    bidNumberContainer: {
        width: 50,
        height: 50,
        backgroundColor: '#1A2B50',
        justifyContent: 'center',
        alignItems: 'center',
    },
    bidNumber: {
        color: COLORS.white,
        fontSize: 24,
        fontWeight: 'bold',
    },
    bidAmountContainer: {
        flex: 1,
        paddingHorizontal: SPACING.md,
        justifyContent: 'center',
    },
    bidAmount: {
        fontSize: 18,
        fontWeight: 'bold',
        color: COLORS.text.primary,
    },
    makeOfferButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#FF5722',
        paddingVertical: 14,
        paddingHorizontal: SPACING.lg,
        borderRadius: 8,
        gap: SPACING.xs,
    },
    section: {
        marginVertical: SPACING.md,
        backgroundColor: COLORS.white,
        borderRadius: 12,
        padding: SPACING.md,
    },
    sectionHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: COLORS.text.primary,
    },
    overviewGrid: {
        marginTop: SPACING.md,
        gap: SPACING.md,
    },
    overviewItem: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: SPACING.md,
    },
    overviewItemText: {
        flex: 1,
    },
    overviewRow: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    overviewLabel: {
        color: COLORS.text.secondary,
        fontSize: 14,
    },
    overviewValue: {
        color: COLORS.text.primary,
        fontSize: 16,
    },
    descriptionText: {
        marginTop: SPACING.md,
        fontSize: 16,
        color: COLORS.text.secondary,
        lineHeight: 24,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: COLORS.background,
    },
    hiddenShareableContainer: {
        position: 'absolute',
        left: -1000, // Move further off-screen
        top: 0,
        opacity: 0.001, // Nearly invisible but still rendered
        zIndex: -1000,
        pointerEvents: 'none',
        backgroundColor: COLORS.white, // Ensure proper background
    },
    shareOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(27, 38, 79, 0.95)', // Use brand color
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000,
        paddingHorizontal: SPACING.lg,
    },
    shareOverlayContent: {
        alignItems: 'center',
        padding: SPACING.xl,
        backgroundColor: COLORS.white,
        borderRadius: 20,
        elevation: 8,
        shadowColor: COLORS.black,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        maxWidth: '90%',
    },
    shareOverlayText: {
        color: COLORS.primary,
        marginTop: SPACING.lg,
        fontSize: 18,
        textAlign: 'center',
        fontWeight: '600',
        letterSpacing: 0.3,
    },
});

// Styles for the shareable vehicle card
const shareableCardStyles = StyleSheet.create({
    container: {
        width: 320, // Reduced width for better mobile compatibility
        backgroundColor: COLORS.white,
        borderRadius: 20,
        overflow: 'hidden',
        elevation: 12,
        shadowColor: COLORS.black,
        shadowOffset: { width: 0, height: 6 },
        shadowOpacity: 0.2,
        shadowRadius: 12,
        borderWidth: 2,
        borderColor: COLORS.border.primary,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        padding: SPACING.lg,
        backgroundColor: COLORS.primary,
        minHeight: 80,
    },
    logoContainer: {
        flex: 1,
        justifyContent: 'center',
    },
    logoText: {
        fontSize: 26,
        fontWeight: 'bold',
        color: COLORS.white,
        letterSpacing: 1,
        marginBottom: 4,
    },
    logoSubtext: {
        fontSize: 12,
        color: COLORS.white,
        opacity: 0.9,
        letterSpacing: 0.5,
    },
    priceContainer: {
        backgroundColor: COLORS.secondary,
        paddingHorizontal: SPACING.md,
        paddingVertical: SPACING.sm,
        borderRadius: 12,
        elevation: 4,
        shadowColor: COLORS.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.15,
        shadowRadius: 4,
        alignItems: 'center',
        minWidth: 120,
    },
    priceLabel: {
        fontSize: 12,
        color: COLORS.white,
        opacity: 0.9,
        marginBottom: 2,
        textAlign: 'center',
    },
    price: {
        fontSize: 18,
        fontWeight: 'bold',
        color: COLORS.white,
        letterSpacing: 0.5,
        textAlign: 'center',
    },
    imageContainer: {
        position: 'relative',
        backgroundColor: COLORS.gray[50],
        aspectRatio: 4 / 3, // Consistent aspect ratio
        justifyContent: 'center',
        alignItems: 'center',
        borderTopWidth: 1,
        borderBottomWidth: 1,
        borderColor: COLORS.border.secondary,
    },
    vehicleImage: {
        width: '100%',
        height: '100%',
    },
    imageOverlay: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        backgroundColor: 'rgba(27, 38, 79, 0.9)', // Use brand color with high opacity
        paddingHorizontal: SPACING.lg,
        paddingVertical: SPACING.md,
        borderBottomLeftRadius: 0,
        borderBottomRightRadius: 0,
    },
    vehicleTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: COLORS.white,
        textAlign: 'center',
        letterSpacing: 0.5,
    },
    detailsContainer: {
        padding: SPACING.lg,
    },
    specsGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
        marginBottom: SPACING.lg,
        backgroundColor: COLORS.gray[50],
        padding: SPACING.md,
        borderRadius: 12,
        gap: SPACING.sm,
    },
    specItem: {
        flexDirection: 'row',
        alignItems: 'center',
        width: '48%',
        backgroundColor: COLORS.white,
        padding: SPACING.sm,
        borderRadius: 8,
        elevation: 2,
        shadowColor: COLORS.black,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.08,
        shadowRadius: 3,
        gap: SPACING.sm,
    },
    specContent: {
        flex: 1,
    },
    specLabel: {
        fontSize: 12,
        color: COLORS.text.secondary,
        marginBottom: 2,
    },
    specValue: {
        fontSize: 14,
        color: COLORS.text.primary,
        fontWeight: '600',
    },
    infoSection: {
        marginBottom: SPACING.lg,
        backgroundColor: COLORS.white,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: COLORS.border.secondary,
        padding: SPACING.md,
    },
    infoRow: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: SPACING.sm,
        gap: SPACING.xs,
    },
    infoLabel: {
        fontSize: 14,
        color: COLORS.text.secondary,
        fontWeight: '500',
        minWidth: 100,
    },
    infoValue: {
        fontSize: 14,
        color: COLORS.text.primary,
        fontWeight: '600',
        flex: 1,
    },
    footer: {
        borderTopWidth: 2,
        borderTopColor: COLORS.border.primary,
        paddingTop: SPACING.md,
        alignItems: 'center',
    },
    ctaContainer: {
        alignItems: 'center',
        gap: SPACING.xs,
    },
    ctaText: {
        fontSize: 16,
        fontWeight: 'bold',
        color: COLORS.primary,
        textAlign: 'center',
        letterSpacing: 0.5,
    },
    websiteText: {
        fontSize: 12,
        color: COLORS.text.secondary,
        textAlign: 'center',
        fontStyle: 'italic',
    },
});
