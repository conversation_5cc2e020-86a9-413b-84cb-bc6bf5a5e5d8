import React, { useEffect, useState, useRef } from 'react';
import {
    View,
    ScrollView,
    Image,
    StyleSheet,
    TouchableOpacity,
    Dimensions,
    Linking,
    Alert,
    Platform,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useLocalSearchParams, router } from 'expo-router';
import { AppDispatch, RootState } from '@/store/store';
import { fetchVehicleDetail } from '@/store/slices/vehicleDetailSlice';
import { ThemedText } from '@/components/common/ThemedText';
import { COLORS, SPACING } from '@/constants/theme';
import LoadingIndicator from '@/components/common/LoadingIndicator';
import { Ionicons } from '@expo/vector-icons';
import { VehicleType } from '@/store/slices/vehicleSlice';
import Constants from 'expo-constants';
import * as Clipboard from 'expo-clipboard';
import { INSURANCE_OPTIONS } from '@/constants/commercialVehicle';
import MakeOfferModal from '@/components/offer/MakeOfferModal';
import OffersList from '@/components/offer/OffersList';
import { resetOffers } from '@/store/slices/offerSlice';
import { useAppSelector } from '@/store/hooks';
import { useNavigation } from '@/contexts/NavigationContext';
import { captureRef } from 'react-native-view-shot';
import * as Sharing from 'expo-sharing';
import Share from 'react-native-share';
import * as FileSystem from 'expo-file-system';

const { width } = Dimensions.get('window');

// Helper function to get insurance type display name
const getInsuranceDisplayName = (insuranceType: string) => {
    const option = INSURANCE_OPTIONS.find(opt => opt.key === insuranceType);
    return option ? option.name : insuranceType;
};

export default function VehicleDetailScreen() {
    const { id, type } = useLocalSearchParams<{ id: string; type: string }>();
    const dispatch = useDispatch<AppDispatch>();
    const { vehicle, loading, error, offers } = useSelector(
        (state: RootState) => state.vehicleDetail
    );
    const { selectedVehicleType } = useSelector((state: RootState) => state.vehicles);
    const { submitSuccess } = useSelector((state: RootState) => state.offer);
    const { isAuthenticated } = useAppSelector((state) => state.auth);
    const { setIntendedDestination } = useNavigation();
    const [selectedImage, setSelectedImage] = useState<string>('');
    const [isOverviewExpanded, setIsOverviewExpanded] = useState(true);
    const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(true);
    const [makeOfferModalVisible, setMakeOfferModalVisible] = useState(false);
    const [isCapturingShare, setIsCapturingShare] = useState(false);
    const shareableViewRef = useRef<View>(null);
    const overlayShareRef = useRef<View>(null);

    useEffect(() => {
        if (id && type) {
            dispatch(fetchVehicleDetail({ id: parseInt(id), type: type as VehicleType }));
        }
    }, [id, type, dispatch]);

    useEffect(() => {
        if (vehicle?.primary_image) {
            setSelectedImage(vehicle.primary_image);
        }
    }, [vehicle]);

    // Reset offers when component unmounts
    useEffect(() => {
        return () => {
            dispatch(resetOffers());
        };
    }, [dispatch]);

    const handleChatPress = () => {
        if (!vehicle?.user?.phone) {
            Alert.alert('Error', 'Phone number not available');
            return;
        }

        // Clean phone number (remove any non-numeric characters)
        const cleanPhoneNumber = vehicle.user.phone.replace(/\D/g, '');

        // Make sure we have a valid phone number
        if (!cleanPhoneNumber || cleanPhoneNumber.length < 10) {
            Alert.alert('Error', 'Invalid phone number');
            return;
        }

        const whatsappMessage = `Hi, I am interested in your ${vehicle?.title} listed on 2ndCar. Is it still available?`;
        const whatsappUrl = `whatsapp://send?phone=91${cleanPhoneNumber}&text=${encodeURIComponent(whatsappMessage)}`;

        Linking.canOpenURL(whatsappUrl)
            .then(supported => {
                if (supported) {
                    return Linking.openURL(whatsappUrl);
                } else {
                    // Try web URL as fallback
                    const webWhatsappUrl = `https://wa.me/91${cleanPhoneNumber}?text=${encodeURIComponent(whatsappMessage)}`;
                    return Linking.openURL(webWhatsappUrl)
                        .catch(() => {
                            throw new Error('WhatsApp is not installed on your device');
                        });
                }
            })
            .catch(error => {
                console.error('Error opening WhatsApp:', error);
                Alert.alert(
                    'Error',
                    'Could not open WhatsApp. Please make sure WhatsApp is installed on your device.',
                    [{ text: 'OK' }]
                );
            });
    };

    const handleCallPress = () => {
        const phoneNumber = vehicle?.user?.phone?.trim();

        if (!phoneNumber) {
            Alert.alert('Error', 'Phone number not available');
            return;
        }

        // iOS simulator check using Expo Constants
        if (Platform.OS === 'ios' && !Constants.isDevice) {
            Alert.alert('Error', 'Phone calls are not supported on the iOS simulator.');
            return;
        }

        // Clean the phone number:
        // Preserve the leading '+' if it exists, else remove all non-numeric characters.
        const cleanedPhoneNumber = phoneNumber.startsWith('+')
            ? '+' + phoneNumber.slice(1).replace(/\D/g, '')
            : phoneNumber.replace(/\D/g, '');

        // Validate: Exclude '+' from the digit count
        const numericPhoneNumber = cleanedPhoneNumber.startsWith('+')
            ? cleanedPhoneNumber.slice(1)
            : cleanedPhoneNumber;
        if (numericPhoneNumber.length < 10) {
            Alert.alert('Error', 'Invalid phone number');
            return;
        }

        // Choose URL scheme based on platform:
        // On iOS, 'telprompt:' shows a prompt; on Android, use 'tel:'.
        const urlScheme = Platform.OS === 'ios' ? 'telprompt:' : 'tel:';
        const phoneUrl = `${urlScheme}${cleanedPhoneNumber}`;

        // Attempt to open the URL directly and handle errors.
        Linking.openURL(phoneUrl).catch((error) => {
            console.error('Failed to open URL:', error);
            Alert.alert(
                'Error',
                'Could not initiate the phone call. Your device may not support this feature.',
                [{ text: 'OK' }]
            );
        });
    };

    const handleSharePress = async () => {
        if (!vehicle) {
            Alert.alert('Error', 'Vehicle details not available');
            return;
        }

        try {
            setIsCapturingShare(true);
            await new Promise(resolve => setTimeout(resolve, 1000));

            if (!overlayShareRef.current) {
                setIsCapturingShare(false);
                throw new Error('Unable to generate share image.');
            }

            const uri = await captureRef(overlayShareRef, {
                format: 'png',
                quality: 0.8,
                result: 'tmpfile',
            });

            setIsCapturingShare(false);
            console.log('Image captured:', uri);

            const deepLink = `https://car.2ndcar.in/api/vehicle/${selectedVehicleType}/${vehicle.id}`;
            const shareMessage = `Check out this ${vehicle?.title} on 2ndCar:\n${deepLink}`;

            const shareOptions = {
                title: vehicle.title || 'Share Vehicle',
                message: shareMessage,
                url: 'file://' + uri,
                type: 'image/png',
            };

            const result = await Share.open(shareOptions);
            console.log('Shared:', result);
        } catch (error) {
            setIsCapturingShare(false);
            Alert.alert('Error', 'Something went wrong while sharing.');
        }
    };

    const handleMakeOffer = () => {
        if (!isAuthenticated) {
            // Set intended destination and show login prompt
            setIntendedDestination(`/(auth)/vehicle/${id}?type=${type}`);
            Alert.alert(
                'Login Required',
                'Please log in to make an offer on this vehicle.',
                [
                    { text: 'Cancel', style: 'cancel' },
                    {
                        text: 'Login',
                        onPress: () => router.push('/auth/register')
                    }
                ]
            );
            return;
        }
        setMakeOfferModalVisible(true);
    };

    const handleOfferSuccess = () => {
        // Close the modal immediately when offer is successful
        setMakeOfferModalVisible(false);

        // Re-fetch vehicle details to get the updated offers
        if (id && type) {
            dispatch(fetchVehicleDetail({ id: parseInt(id), type: type as VehicleType }));
        }
    };

    // Clear offer success state when modal is closed to prevent unwanted effects
    useEffect(() => {
        if (!makeOfferModalVisible && submitSuccess) {
            // Clear the success state to prevent any side effects
            dispatch(resetOffers());
        }
    }, [makeOfferModalVisible, submitSuccess, dispatch]);

    if (loading) {
        return (
            <View style={styles.loadingContainer}>
                <LoadingIndicator size={120} color1="#ff6b6b" color2="#4ecdc4" />
            </View>
        );
    }

    if (error || !vehicle)
        return <ThemedText>Error: {error}</ThemedText>;

    // Shareable Vehicle Card Component
    const ShareableVehicleCard = () => (
        <View style={shareableCardStyles.container}>
            {/* Header with Logo */}
            <View style={shareableCardStyles.header}>
                <View style={shareableCardStyles.logoContainer}>
                    <ThemedText style={shareableCardStyles.logoText}>2ndCar</ThemedText>
                </View>
                <View style={shareableCardStyles.priceContainer}>
                    <ThemedText style={shareableCardStyles.price}>
                        ₹{parseInt(vehicle.price).toLocaleString('en-IN')}
                    </ThemedText>
                </View>
            </View>

            {/* Vehicle Image */}
            <Image
                source={{
                    uri: vehicle?.primary_image || 'https://placeholder.com/300x200',
                }}
                style={shareableCardStyles.vehicleImage}
                resizeMode="cover"
            />

            {/* Vehicle Details */}
            <View style={shareableCardStyles.detailsContainer}>
                <ThemedText style={shareableCardStyles.title}>{vehicle.title}</ThemedText>

                {/* Key Details Grid */}
                <View style={shareableCardStyles.detailsGrid}>
                    <View style={shareableCardStyles.detailItem}>
                        <Ionicons name="calendar-outline" size={16} color={COLORS.text.secondary} />
                        <ThemedText style={shareableCardStyles.detailText}>{vehicle.year}</ThemedText>
                    </View>
                    <View style={shareableCardStyles.detailItem}>
                        <Ionicons name="speedometer-outline" size={16} color={COLORS.text.secondary} />
                        <ThemedText style={shareableCardStyles.detailText}>{vehicle.kilometers_driven} km</ThemedText>
                    </View>
                    <View style={shareableCardStyles.detailItem}>
                        <Ionicons name="car-outline" size={16} color={COLORS.text.secondary} />
                        <ThemedText style={shareableCardStyles.detailText}>{vehicle.fuel_type}</ThemedText>
                    </View>
                    <View style={shareableCardStyles.detailItem}>
                        <Ionicons name="person-outline" size={16} color={COLORS.text.secondary} />
                        <ThemedText style={shareableCardStyles.detailText}>{vehicle.ownership}</ThemedText>
                    </View>
                </View>

                {/* Additional Details */}
                <View style={shareableCardStyles.additionalDetails}>
                    <View style={shareableCardStyles.detailRow}>
                        <ThemedText style={shareableCardStyles.detailLabel}>Brand:</ThemedText>
                        <ThemedText style={shareableCardStyles.detailValue}>{vehicle.brand || 'N/A'}</ThemedText>
                    </View>
                    <View style={shareableCardStyles.detailRow}>
                        <ThemedText style={shareableCardStyles.detailLabel}>Model:</ThemedText>
                        <ThemedText style={shareableCardStyles.detailValue}>{vehicle.model || 'N/A'}</ThemedText>
                    </View>
                    <View style={shareableCardStyles.detailRow}>
                        <ThemedText style={shareableCardStyles.detailLabel}>Variant:</ThemedText>
                        <ThemedText style={shareableCardStyles.detailValue}>{vehicle.variant || 'N/A'}</ThemedText>
                    </View>
                    <View style={shareableCardStyles.detailRow}>
                        <ThemedText style={shareableCardStyles.detailLabel}>Location:</ThemedText>
                        <ThemedText style={shareableCardStyles.detailValue}>{vehicle.location}</ThemedText>
                    </View>
                    {vehicle?.insurance_type && (
                        <View style={shareableCardStyles.detailRow}>
                            <ThemedText style={shareableCardStyles.detailLabel}>Insurance:</ThemedText>
                            <ThemedText style={shareableCardStyles.detailValue}>
                                {getInsuranceDisplayName(vehicle.insurance_type)}
                            </ThemedText>
                        </View>
                    )}
                </View>

                {/* Footer */}
                <View style={shareableCardStyles.footer}>
                    <ThemedText style={shareableCardStyles.footerText}>
                        Find more cars at 2ndCar.in
                    </ThemedText>
                </View>
            </View>
        </View>
    );

    return (
        <View style={styles.container}>
            {/* Hidden Shareable View for Capture */}
            <View style={styles.hiddenShareableContainer}>
                <View
                    ref={shareableViewRef}
                    collapsable={false}
                >
                    <ShareableVehicleCard />
                </View>
            </View>

            {/* Temporary Overlay for Share Capture */}
            {isCapturingShare && (
                <View style={styles.shareOverlay}>
                    <View style={styles.shareOverlayContent}>
                        <View ref={overlayShareRef} collapsable={false}>
                            <ShareableVehicleCard />
                        </View>
                        <ThemedText style={styles.shareOverlayText}>Generating image...</ThemedText>
                    </View>
                </View>
            )}

            {/* Header Overlay */}
            <View style={styles.header}>
                <View style={styles.headerActions}>
                    {/* <TouchableOpacity style={styles.iconButton}>
                        <Ionicons name="heart-outline" size={24} color={COLORS.white} />
                    </TouchableOpacity> */}
                    <TouchableOpacity style={styles.iconButton} onPress={handleSharePress}>
                        <Ionicons name="share-social-outline" size={24} color={COLORS.white} />
                    </TouchableOpacity>
                </View>
            </View>

            <ScrollView showsVerticalScrollIndicator={false}>
                {/* Main Image */}
                <Image
                    source={{
                        uri:
                            selectedImage ||
                            vehicle?.primary_image ||
                            'https://placeholder.com/300x200',
                    }}
                    style={styles.mainImage}
                />

                {/* Thumbnail Images */}
                <ScrollView
                    horizontal
                    style={styles.thumbnailContainer}
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={styles.thumbnailContentContainer}
                >
                    {vehicle.images.map((image, index) => (
                        <TouchableOpacity key={index} onPress={() => setSelectedImage(image)}>
                            <Image
                                source={{ uri: image }}
                                style={[
                                    styles.thumbnail,
                                    selectedImage === image && styles.selectedThumbnail,
                                ]}
                                resizeMode="cover"
                            />
                        </TouchableOpacity>
                    ))}
                </ScrollView>

                {/* Vehicle Info */}
                <View style={styles.infoContainer}>
                    <View style={styles.titleRow}>
                        <View style={styles.titleContainer}>
                            <ThemedText style={styles.title}>{vehicle.title}</ThemedText>
                            <ThemedText style={styles.variant}>{vehicle.variant}</ThemedText>
                        </View>
                    </View>

                    {/* Price and Actions */}
                    <View style={styles.actionContainer}>
                        <View style={styles.priceContainer}>
                            <ThemedText style={styles.price}>
                                ₹ {parseInt(vehicle.price).toLocaleString('en-IN')}
                            </ThemedText>
                        </View>
                        <TouchableOpacity style={styles.actionButton} onPress={handleChatPress}>
                            <Ionicons
                                name="chatbubble-ellipses-outline"
                                size={20}
                                color={COLORS.white}
                            />
                            <ThemedText style={styles.buttonText}>Chat</ThemedText>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.actionButton} onPress={handleCallPress}>
                            <Ionicons name="call-outline" size={20} color={COLORS.white} />
                            <ThemedText style={styles.buttonText}>Call</ThemedText>
                        </TouchableOpacity>
                    </View>

                    {/* Offers Section */}
                    <OffersList
                        offers={offers}
                        onPlaceOffer={handleMakeOffer}
                    />

                    {/* Overview Section */}
                    <View style={styles.section}>
                        <TouchableOpacity
                            style={styles.sectionHeader}
                            onPress={() => setIsOverviewExpanded(!isOverviewExpanded)}
                        >
                            <ThemedText style={styles.sectionTitle}>Vehicle Overview</ThemedText>
                            <Ionicons
                                name={isOverviewExpanded ? 'chevron-up' : 'chevron-down'}
                                size={24}
                                color={COLORS.text.secondary}
                            />
                        </TouchableOpacity>

                        {isOverviewExpanded && (
                            <View style={styles.overviewGrid}>
                                <View style={styles.overviewItem}>
                                    <Ionicons name="car-outline" size={24} color={COLORS.text.secondary} />
                                    <View style={styles.overviewItemText}>
                                        <ThemedText style={styles.overviewRow}>
                                            <ThemedText style={styles.overviewLabel}>Fuel Type: </ThemedText>
                                            <ThemedText style={styles.overviewValue}>{vehicle.fuel_type}</ThemedText>
                                        </ThemedText>
                                    </View>
                                </View>
                                <View style={styles.overviewItem}>
                                    <Ionicons name="speedometer-outline" size={24} color={COLORS.text.secondary} />
                                    <View style={styles.overviewItemText}>
                                        <ThemedText style={styles.overviewRow}>
                                            <ThemedText style={styles.overviewLabel}>KMs Driven: </ThemedText>
                                            <ThemedText style={styles.overviewValue}>{vehicle.kilometers_driven} KM</ThemedText>
                                        </ThemedText>
                                    </View>
                                </View>
                                <View style={styles.overviewItem}>
                                    <Ionicons name="settings-outline" size={24} color={COLORS.text.secondary} />
                                    <View style={styles.overviewItemText}>
                                        <ThemedText style={styles.overviewRow}>
                                            <ThemedText style={styles.overviewLabel}>Transmission: </ThemedText>
                                            <ThemedText style={styles.overviewValue}>{vehicle?.transmission}</ThemedText>
                                        </ThemedText>
                                    </View>
                                </View>
                                <View style={styles.overviewItem}>
                                    <Ionicons name="person-outline" size={24} color={COLORS.text.secondary} />
                                    <View style={styles.overviewItemText}>
                                        <ThemedText style={styles.overviewRow}>
                                            <ThemedText style={styles.overviewLabel}>No. of Owners: </ThemedText>
                                            <ThemedText style={styles.overviewValue}>{vehicle.ownership}</ThemedText>
                                        </ThemedText>
                                    </View>
                                </View>
                                <View style={styles.overviewItem}>
                                    <Ionicons name="calendar-outline" size={24} color={COLORS.text.secondary} />
                                    <View style={styles.overviewItemText}>
                                        <ThemedText style={styles.overviewRow}>
                                            <ThemedText style={styles.overviewLabel}>Year: </ThemedText>
                                            <ThemedText style={styles.overviewValue}>{vehicle?.year}</ThemedText>
                                        </ThemedText>
                                    </View>
                                </View>
                                <View style={styles.overviewItem}>
                                    <Ionicons name="location-outline" size={24} color={COLORS.text.secondary} />
                                    <View style={styles.overviewItemText}>
                                        <ThemedText style={styles.overviewRow}>
                                            <ThemedText style={styles.overviewLabel}>Location: </ThemedText>
                                            <ThemedText style={styles.overviewValue}>{vehicle?.location}</ThemedText>
                                        </ThemedText>
                                    </View>
                                </View>
                                {vehicle?.insurance_type && (
                                    <View style={styles.overviewItem}>
                                        <Ionicons name="shield-checkmark-outline" size={24} color={COLORS.text.secondary} />
                                        <View style={styles.overviewItemText}>
                                            <ThemedText style={styles.overviewRow}>
                                                <ThemedText style={styles.overviewLabel}>Insurance: </ThemedText>
                                                <ThemedText style={styles.overviewValue}>
                                                    {getInsuranceDisplayName(vehicle.insurance_type)}
                                                </ThemedText>
                                            </ThemedText>
                                        </View>
                                    </View>
                                )}
                            </View>
                        )}
                    </View>

                    {/* Description Section */}
                    <View style={styles.section}>
                        <TouchableOpacity
                            style={styles.sectionHeader}
                            onPress={() => setIsDescriptionExpanded(!isDescriptionExpanded)}
                        >
                            <ThemedText style={styles.sectionTitle}>Description</ThemedText>
                            <Ionicons
                                name={isDescriptionExpanded ? 'chevron-up' : 'chevron-down'}
                                size={24}
                                color={COLORS.text.secondary}
                            />
                        </TouchableOpacity>
                        {isDescriptionExpanded && (
                            <ThemedText style={styles.descriptionText}>
                                {vehicle?.description || 'No description available'}
                            </ThemedText>
                        )}
                    </View>
                </View>
            </ScrollView>

            {/* Make Offer Modal */}
            {vehicle && (
                <MakeOfferModal
                    visible={makeOfferModalVisible}
                    onClose={() => setMakeOfferModalVisible(false)}
                    vehicleId={vehicle.id}
                    vehicleType={type || 'car'}
                    vehiclePrice={parseInt(vehicle.price)}
                    onOfferSuccess={handleOfferSuccess}
                />
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        backgroundColor: COLORS.background,
        position: 'relative',
    },
    header: {
        position: 'absolute',
        top: SPACING.md,
        right: SPACING.md,
        zIndex: 10,
        flexDirection: 'row',
    },
    headerActions: {
        flexDirection: 'row',
        gap: SPACING.sm,
    },
    iconButton: {
        padding: SPACING.xs,
        backgroundColor: 'rgba(0,0,0,0.5)',
        borderRadius: 20,
        marginLeft: SPACING.sm,
    },
    mainImage: {
        width: width,
        height: width * 0.75,
    },
    thumbnailContainer: {
        backgroundColor: COLORS.background,
        paddingHorizontal: SPACING.sm,
        paddingVertical: SPACING.xs,
        marginVertical: SPACING.sm,
        minHeight: 80,
    },
    thumbnailContentContainer: {
        alignItems: 'center',
    },
    thumbnail: {
        width: 60,
        height: 60,
        marginRight: SPACING.xs,
        borderRadius: 4,
    },
    selectedThumbnail: {
        borderWidth: 2,
        borderColor: COLORS.primary,
    },
    infoContainer: {
        padding: SPACING.md,
    },
    titleRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: SPACING.md,
    },
    titleContainer: {
        flex: 1,
    },
    title: {
        fontSize: 20,
        fontWeight: 'bold',
        color: COLORS.text.primary,
    },
    variant: {
        color: COLORS.text.secondary,
        marginTop: SPACING.xs,
    },
    actionContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: SPACING.md,
    },
    priceContainer: {
        flex: 1,
    },
    price: {
        fontSize: 24,
        fontWeight: 'bold',
        color: COLORS.primary,
    },
    actionButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: COLORS.primary,
        padding: SPACING.sm,
        borderRadius: 8,
        marginLeft: SPACING.sm,
    },
    buttonText: {
        color: COLORS.white,
        marginLeft: SPACING.xs,
        fontSize: 16,
        fontWeight: 'bold',
    },
    bidSection: {
        marginVertical: SPACING.md,
        width: '100%',
    },
    bidSectionTitle: {
        fontSize: 16,
        marginBottom: SPACING.sm,
        color: COLORS.text.primary,
    },
    bidContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        gap: SPACING.md,
    },
    bidInfoContainer: {
        flexDirection: 'row',
        flex: 1,
        borderWidth: 0.4,
        borderRadius: 8,
        borderColor: '#1A2B50',
        overflow: 'hidden', // Ensures the bidNumberContainer respects the border radius
    },
    bidNumberContainer: {
        width: 50,
        height: 50,
        backgroundColor: '#1A2B50',
        justifyContent: 'center',
        alignItems: 'center',
    },
    bidNumber: {
        color: COLORS.white,
        fontSize: 24,
        fontWeight: 'bold',
    },
    bidAmountContainer: {
        flex: 1,
        paddingHorizontal: SPACING.md,
        justifyContent: 'center',
    },
    bidAmount: {
        fontSize: 18,
        fontWeight: 'bold',
        color: COLORS.text.primary,
    },
    makeOfferButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#FF5722',
        paddingVertical: 14,
        paddingHorizontal: SPACING.lg,
        borderRadius: 8,
        gap: SPACING.xs,
    },
    section: {
        marginVertical: SPACING.md,
        backgroundColor: COLORS.white,
        borderRadius: 12,
        padding: SPACING.md,
    },
    sectionHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: COLORS.text.primary,
    },
    overviewGrid: {
        marginTop: SPACING.md,
        gap: SPACING.md,
    },
    overviewItem: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: SPACING.md,
    },
    overviewItemText: {
        flex: 1,
    },
    overviewRow: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    overviewLabel: {
        color: COLORS.text.secondary,
        fontSize: 14,
    },
    overviewValue: {
        color: COLORS.text.primary,
        fontSize: 16,
    },
    descriptionText: {
        marginTop: SPACING.md,
        fontSize: 16,
        color: COLORS.text.secondary,
        lineHeight: 24,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: COLORS.background,
    },
    hiddenShareableContainer: {
        position: 'absolute',
        left: -400,
        top: 0,
        opacity: 0.01, // Almost invisible but still rendered
        zIndex: -1,
        pointerEvents: 'none',
    },
    shareOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0,0,0,0.9)',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000,
    },
    shareOverlayContent: {
        alignItems: 'center',
        padding: SPACING.lg,
    },
    shareOverlayText: {
        color: COLORS.white,
        marginTop: SPACING.lg,
        fontSize: 16,
        textAlign: 'center',
        fontWeight: '500',
    },
});

// Styles for the shareable vehicle card
const shareableCardStyles = StyleSheet.create({
    container: {
        width: 350,
        backgroundColor: COLORS.white,
        borderRadius: 16,
        overflow: 'hidden',
        elevation: 8,
        shadowColor: COLORS.black,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 8,
        borderWidth: 1,
        borderColor: COLORS.border.primary,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: SPACING.md,
        backgroundColor: COLORS.primary,
        position: 'relative',
    },
    logoContainer: {
        flex: 1,
    },
    logoText: {
        fontSize: 22,
        fontWeight: 'bold',
        color: COLORS.white,
        letterSpacing: 0.5,
    },
    priceContainer: {
        backgroundColor: COLORS.secondary,
        paddingHorizontal: SPACING.md,
        paddingVertical: SPACING.sm,
        borderRadius: 25,
        elevation: 2,
        shadowColor: COLORS.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
    },
    price: {
        fontSize: 18,
        fontWeight: 'bold',
        color: COLORS.white,
        letterSpacing: 0.3,
    },
    vehicleImage: {
        width: '100%',
        height: 200,
    },
    detailsContainer: {
        padding: SPACING.md,
    },
    title: {
        fontSize: 20,
        fontWeight: 'bold',
        color: COLORS.text.primary,
        marginBottom: SPACING.md,
        textAlign: 'center',
        letterSpacing: 0.3,
    },
    detailsGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
        marginBottom: SPACING.md,
        backgroundColor: COLORS.gray[50],
        padding: SPACING.sm,
        borderRadius: 8,
    },
    detailItem: {
        flexDirection: 'row',
        alignItems: 'center',
        width: '48%',
        marginBottom: SPACING.xs,
        gap: SPACING.xs,
        backgroundColor: COLORS.white,
        padding: SPACING.xs,
        borderRadius: 6,
        elevation: 1,
        shadowColor: COLORS.black,
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05,
        shadowRadius: 2,
    },
    detailText: {
        fontSize: 12,
        color: COLORS.text.secondary,
        flex: 1,
        fontWeight: '500',
    },
    additionalDetails: {
        borderTopWidth: 1,
        borderTopColor: COLORS.border.secondary,
        paddingTop: SPACING.sm,
        marginBottom: SPACING.sm,
    },
    detailRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: SPACING.xs,
    },
    detailLabel: {
        fontSize: 12,
        color: COLORS.text.secondary,
        flex: 1,
    },
    detailValue: {
        fontSize: 12,
        color: COLORS.text.primary,
        fontWeight: '500',
        flex: 1,
        textAlign: 'right',
    },
    footer: {
        borderTopWidth: 1,
        borderTopColor: COLORS.border.secondary,
        paddingTop: SPACING.sm,
        alignItems: 'center',
    },
    footerText: {
        fontSize: 12,
        color: COLORS.text.secondary,
        fontStyle: 'italic',
    },
});
