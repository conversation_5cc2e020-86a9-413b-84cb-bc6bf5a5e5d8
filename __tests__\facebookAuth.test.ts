import { registerWithGoogle } from "@/store/slices/authSlice";

// Mock the environment variables
process.env.EXPO_PUBLIC_APP_ID = "1215086369859674";
process.env.EXPO_PUBLIC_API_BASE_URL = "https://car.2ndcar.in/api";

// Mock AsyncStorage
jest.mock("@react-native-async-storage/async-storage", () => ({
  setItem: jest.fn(),
  getItem: jest.fn(),
  removeItem: jest.fn(),
}));

// Mock fetch
global.fetch = jest.fn();

// Mock Redux store
const mockDispatch = jest.fn();
const mockGetState = jest.fn();

describe("Facebook Authentication", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test("should have Facebook App ID configured", () => {
    expect(process.env.EXPO_PUBLIC_APP_ID).toBe("1215086369859674");
  });

  test("registerWithGoogle should be defined (used for Facebook auth)", () => {
    expect(registerWithGoogle).toBeDefined();
    expect(typeof registerWithGoogle).toBe("function");
  });

  test("should handle successful Facebook registration using Google endpoint", async () => {
    const mockResponse = {
      status: true,
      user: { id: 1, name: "Test User", email: "<EMAIL>" },
      authorisation: { token: "test-token" },
    };

    (global.fetch as jest.Mock).mockResolvedValueOnce({
      json: jest.fn().mockResolvedValueOnce(mockResponse),
    });

    const thunk = registerWithGoogle("test-uid");
    const result = await thunk(mockDispatch, mockGetState, undefined);

    expect(result.payload).toEqual(mockResponse);
  });

  test("should handle Facebook registration requiring phone verification using Google endpoint", async () => {
    const mockResponse = {
      status: false,
      user: {
        id: 1,
        name: "Test User",
        email: "<EMAIL>",
        status: "0",
      },
      requiresPhone: true,
      uid: "test-uid",
    };

    (global.fetch as jest.Mock).mockResolvedValueOnce({
      json: jest.fn().mockResolvedValueOnce(mockResponse),
    });

    const thunk = registerWithGoogle("test-uid");
    const result = await thunk(mockDispatch, mockGetState, undefined);

    expect(result.payload.requiresPhone).toBe(true);
    expect(result.payload.uid).toBe("test-uid");
  });

  test("should handle Facebook registration error using Google endpoint", async () => {
    const mockError = new Error("Registration failed");
    (global.fetch as jest.Mock).mockRejectedValueOnce(mockError);

    const thunk = registerWithGoogle("test-uid");

    try {
      await thunk(mockDispatch, mockGetState, undefined);
    } catch (error: any) {
      expect(error.message).toBe("Registration failed");
    }
  });
});
