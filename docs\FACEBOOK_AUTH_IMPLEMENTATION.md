# Facebook Authentication Implementation

## Overview
This document outlines the Facebook authentication implementation in the React Native app using Firebase and best practices.

## Features Implemented

### 1. Firebase Configuration
- **File**: `config/firebase.ts`
- Added `FacebookAuthProvider` import
- Created `facebookProvider` instance
- Exported both Google and Facebook providers

### 2. Redux Store Integration
- **File**: `store/slices/authSlice.ts`
- Uses existing `registerWithGoogle` async thunk action for Facebook authentication
- Handles user registration with Facebook UID using same endpoint as Google
- Supports phone verification flow for new users
- Proper error handling and state management
- No additional reducer cases needed (reuses Google authentication flow)

### 3. Main Register Screen
- **File**: `app/auth/register.tsx`
- Added Facebook authentication imports and configuration
- Implemented Facebook auth request using `expo-auth-session/providers/facebook`
- Added Facebook loading states and handlers
- Created `registerFacebookUserFlow` function
- Added `handleFacebookAuthResponse` and `handleFacebookSignIn` functions
- Updated UI to include Facebook sign-in button
- Added loading overlay support for Facebook authentication

### 4. Social Buttons Component
- **File**: `components/auth/SocialButtons.tsx`
- Updated to support both Google and Facebook authentication
- Added Facebook configuration and handlers
- Consistent UI with both authentication options
- Proper loading states and error handling

### 5. Utility Functions
- **File**: `utils/facebookAuth.ts`
- Created `useFacebookAuth` hook for reusable Facebook authentication
- Standalone `facebookSignIn` function
- Proper error handling and Firebase integration
- Support for phone verification flow

### 6. App Configuration
- **File**: `app.json`
- Updated scheme configuration
- Added iOS URL schemes for Facebook (`fb${EXPO_PUBLIC_APP_ID}`)
- Added Android intent filters for Facebook authentication
- Proper deep linking support

### 7. Environment Variables
- **File**: `.env`
- `EXPO_PUBLIC_APP_ID=1215086369859674` (Facebook App ID)
- All necessary environment variables configured

## Authentication Flow

### 1. User Interaction
1. User taps "Continue with Facebook" button
2. Loading state is activated
3. Facebook authentication modal opens

### 2. Facebook Authentication
1. User authenticates with Facebook
2. Facebook returns access token
3. Access token is used to create Firebase credential
4. User is signed into Firebase

### 3. Backend Registration
1. Firebase UID is sent to backend API (`/api/register-google`) - same endpoint for both Google and Facebook
2. Backend checks if user exists
3. If new user: requires phone verification
4. If existing user: returns authentication token

### 4. Navigation
1. Existing users: Navigate to main app
2. New users: Navigate to phone verification screen
3. After phone verification: Navigate to main app

## API Integration

### Registration Endpoint (Shared with Google)
```
POST /api/register-google
Content-Type: application/json

{
  "uid": "firebase_user_uid"
}
```

**Note**: Facebook authentication uses the same backend endpoint as Google authentication since both provide Firebase UIDs.

### Response Format
```json
{
  "status": true,
  "user": {
    "id": 1,
    "name": "User Name",
    "email": "<EMAIL>",
    "status": "1"
  },
  "authorisation": {
    "token": "jwt_token",
    "type": "bearer"
  }
}
```

## Security Best Practices

### 1. Environment Variables
- Facebook App ID stored in environment variables
- No hardcoded sensitive information
- Proper environment variable validation

### 2. Firebase Integration
- Uses Firebase Authentication for secure token management
- Proper credential creation with Facebook access token
- Secure UID generation and validation

### 3. Error Handling
- Comprehensive error handling for all authentication steps
- User-friendly error messages
- Proper loading states and UI feedback

### 4. Token Management
- Secure token storage using AsyncStorage
- Automatic token refresh through Firebase
- Proper token validation

## Testing

### 1. Unit Tests
- **File**: `__tests__/facebookAuth.test.ts`
- Tests for Facebook registration action
- Mock implementations for external dependencies
- Error handling validation

### 2. Integration Testing
- Facebook authentication flow
- Phone verification integration
- Navigation flow testing

## Configuration Requirements

### 1. Facebook Developer Console
- App ID: `1215086369859674`
- Proper OAuth redirect URIs configured
- Platform-specific configurations (iOS/Android)

### 2. Firebase Console
- Facebook authentication provider enabled
- Proper OAuth configuration
- Security rules configured

### 3. Backend API
- `/api/register-facebook` endpoint implemented
- Phone verification endpoint (`/api/register-phone`)
- Proper user management and token generation

## Usage Examples

### Basic Facebook Sign-In
```typescript
import { useFacebookAuth } from '@/utils/facebookAuth';

const { signInWithFacebook, loading, error } = useFacebookAuth();

const handleFacebookLogin = async () => {
  const result = await signInWithFacebook();
  if (result.success) {
    // Handle successful authentication
  }
};
```

### Redux Integration
```typescript
import { useAppDispatch } from '@/store/hooks';
import { registerWithGoogle } from '@/store/slices/authSlice';

const dispatch = useAppDispatch();

const handleFacebookAuth = async (uid: string) => {
  // Use the same registerWithGoogle action for Facebook authentication
  const result = await dispatch(registerWithGoogle(uid)).unwrap();
  // Handle result
};
```

## Troubleshooting

### Common Issues
1. **Facebook App ID not configured**: Check environment variables
2. **Redirect URI mismatch**: Verify app.json configuration
3. **Firebase authentication failed**: Check Firebase console settings
4. **Phone verification required**: Normal flow for new users

### Debug Steps
1. Check console logs for detailed error messages
2. Verify environment variables are loaded
3. Test Facebook authentication in development
4. Validate Firebase configuration

## Future Enhancements
1. Add Facebook profile picture integration
2. Implement Facebook friends integration
3. Add social sharing features
4. Enhanced error recovery mechanisms
