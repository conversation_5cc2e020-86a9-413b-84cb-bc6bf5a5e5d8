import React, { useState, useEffect } from 'react';
import {
    Alert,
    Dimensions,
    Image,
    ImageStyle,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StyleSheet,
    View,
    TextInput,
    ActivityIndicator,
    TouchableOpacity,
    ViewStyle
} from 'react-native';
import EmailForm from '@/components/auth/EmailForm';
import ForgotPasswordForm from '@/components/auth/ForgotPasswordForm';
import LoginForm from '@/components/auth/LoginForm';
import RegisterForm from '@/components/auth/RegisterForm';
import ResetPasswordForm from '@/components/auth/ResetPasswordForm';
import VerifyForgotPasswordOtpForm from '@/components/auth/VerifyForgotPasswordOtpForm';
import VerifyOtpForm from '@/components/auth/VerifyOtpForm';
import LoadingIndicator from '@/components/common/LoadingIndicator';
import ThemedButton from '@/components/common/ThemedButton';
import { ThemedText } from '@/components/common/ThemedText';
import { COLORS, SPACING } from '@/constants/theme';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
    checkEmail,
    forgotPassword,
    login,
    register,
    resetPassword,
    verifyForgotPasswordOtp,
    verifyOtp,
    registerWithGoogle,
    registerWithPhone
} from '@/store/slices/authSlice';
import { useRouter } from 'expo-router';
import * as Google from 'expo-auth-session/providers/google';
import * as Facebook from 'expo-auth-session/providers/facebook';
import * as WebBrowser from 'expo-web-browser';
import { makeRedirectUri, exchangeCodeAsync, ResponseType } from 'expo-auth-session';
import { GoogleAuthProvider, FacebookAuthProvider, signInWithCredential } from 'firebase/auth';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { auth } from '../../config/firebase';
import { Ionicons } from '@expo/vector-icons';
import Constants from 'expo-constants';

// Complete any in-progress auth session (important for web)
WebBrowser.maybeCompleteAuthSession();

// Replace the existing BackButton component with this enhanced version
const BackButton = ({ onPress }: { onPress: () => void }) => (
    <TouchableOpacity
        onPress={onPress}
        style={{
            position: 'absolute',
            left: SPACING.lg,
            top: Platform.OS === 'ios' ? 50 : 50,
            zIndex: 1,
            padding: SPACING.xs,
            backgroundColor: COLORS.background,
            borderRadius: 20,
            elevation: 2,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.2,
            shadowRadius: 2,
        }}
    >
        <Ionicons name="arrow-back" size={24} color={COLORS.text.primary} />
    </TouchableOpacity>
);

// First, create a helper function to get the client ID
const getClientId = () => {
    const clientId = Platform.select({
        ios: process.env.EXPO_PUBLIC_IOS_CLIENT_ID,
        android: process.env.EXPO_PUBLIC_ANDROID_CLIENT_ID,
        default: process.env.EXPO_PUBLIC_WEB_CLIENT_ID
    });

    if (!clientId) {
        throw new Error('Client ID not configured for this platform');
    }

    return clientId;
};

export default function RegisterScreen() {
    const router = useRouter();
    const dispatch = useAppDispatch();
    const { loading } = useAppSelector((state) => state.auth);

    // Existing states for email flow
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [otp, setOtp] = useState('');
    const [step, setStep] = useState<
        'email' | 'login' | 'register' | 'verify' | 'forgotPassword' | 'verifyForgotPassword' | 'resetPassword'
    >('email');
    const [loader, setLoader] = useState(false);

    // States for Google sign‑in and phone verification
    const [phoneRequired, setPhoneRequired] = useState(false);
    const [pendingUid, setPendingUid] = useState<string | null>(null);
    const [phone, setPhone] = useState('');

    // Add Google loading state
    const [googleLoading, setGoogleLoading] = useState(false);

    // Add Facebook loading state
    const [facebookLoading, setFacebookLoading] = useState(false);

    // Configure the Google auth request.
    const [request, response, promptAsync] = Google.useAuthRequest({
        webClientId: process.env.EXPO_PUBLIC_WEB_CLIENT_ID,
        androidClientId: process.env.EXPO_PUBLIC_ANDROID_CLIENT_ID,
        iosClientId: process.env.EXPO_PUBLIC_IOS_CLIENT_ID,
        responseType: Platform.select({ web: 'token', default: 'code' }),
        redirectUri: makeRedirectUri({
            scheme: 'com.factcoding.secondcar',
            path: ''
        }),
        scopes: ['profile', 'email']
    });

    // Configure the Facebook auth request.
    const [facebookRequest, facebookResponse, facebookPromptAsync] = Facebook.useAuthRequest({
        clientId: process.env.EXPO_PUBLIC_APP_ID || '',
        responseType: ResponseType.Token,
        scopes: ['public_profile', 'email'],
        redirectUri: makeRedirectUri({
            scheme: 'com.factcoding.secondcar',
            path: '/auth/facebook'
        }),
    });



    // Listen for changes in the Google auth response.
    useEffect(() => {
        if (response?.type === 'success') {
            handleAuthResponse(response);
        } else if (response?.type === 'error') {
            Alert.alert('Authentication Error', response.error?.message || 'Failed to authenticate with Google');
        }
    }, [response]);

    // ----- Email/Password Flow Functions -----

    const handleEmailSubmit = async (email: string) => {
        setLoader(true);
        try {
            setEmail(email);
            const result = await dispatch(checkEmail(email)).unwrap();
            if (result.status) {
                setStep('login');
            } else {
                setStep('register');
            }
        } catch (error: any) {
            if (error.message === 'User not found') {
                setStep('register');
            } else {
                Alert.alert('Error', error.message || 'Failed to check email');
            }
        } finally {
            setLoader(false);
        }
    };

    const handleLogin = async () => {
        if (!password) {
            Alert.alert('Error', 'Please enter your password');
            return;
        }
        try {
            setLoader(true);
            const result = await dispatch(login({ email, password })).unwrap();
            if (result.status) {
                router.replace('/(auth)/(tabs)');
            } else {
                Alert.alert('Error', result.message);
            }
        } catch (error: any) {
            Alert.alert('Error', error.message || 'Login failed');
        } finally {
            setLoader(false);
        }
    };

    const handleRegister = async (values: { name: string; phone: string; password: string }) => {
        try {
            setLoader(true);
            const result = await dispatch(
                register({
                    email,
                    name: values.name,
                    phone: values.phone,
                    password: values.password
                })
            ).unwrap();
            if (result.status) {
                setStep('verify');
            } else {
                Alert.alert('Error', result.message);
            }
        } catch (error: any) {
            Alert.alert('Error', error.message || 'Registration failed');
        } finally {
            setLoader(false);
        }
    };

    const handleVerifyOtp = async () => {
        if (!otp) {
            Alert.alert('Error', 'Please enter the OTP');
            return;
        }
        try {
            setLoader(true);
            const result = await dispatch(verifyOtp({ email, otp })).unwrap();
            if (result.status) {
                router.replace('/(auth)/(tabs)');
            } else {
                Alert.alert('Error', result.message);
            }
        } catch (error: any) {
            Alert.alert('Error', error.message || 'OTP verification failed');
        } finally {
            setLoader(false);
        }
    };

    const handleForgotPassword = async () => {
        try {
            setLoader(true);
            const result = await dispatch(forgotPassword(email)).unwrap();
            if (result.status) {
                setStep('verifyForgotPassword');
            } else {
                Alert.alert('Error', result.message);
            }
        } catch (error: any) {
            Alert.alert('Error', error.message || 'Failed to send OTP');
        } finally {
            setLoader(false);
        }
    };

    const handleVerifyForgotPasswordOtp = async () => {
        try {
            setLoader(true);
            const result = await dispatch(verifyForgotPasswordOtp({ email, otp })).unwrap();
            if (result.status) {
                setStep('resetPassword');
            } else {
                Alert.alert('Error', result.message);
            }
        } catch (error: any) {
            Alert.alert('Error', error.message || 'OTP verification failed');
        } finally {
            setLoader(false);
        }
    };

    const handleResetPassword = async (values: { password: string; confirmPassword: string }) => {
        if (values.password !== values.confirmPassword) {
            Alert.alert('Error', 'Passwords do not match');
            return;
        }
        try {
            setLoader(true);
            const result = await dispatch(
                resetPassword({
                    email,
                    newPassword: values.password,
                    otp
                })
            ).unwrap();
            if (result.status) {
                router.replace('/(auth)/(tabs)');
            } else {
                Alert.alert('Error', result.message);
            }
        } catch (error: any) {
            Alert.alert('Error', error.message || 'Password reset failed');
        } finally {
            setLoader(false);
        }
    };

    // ----- Google Sign-In & Phone Verification Functions -----

    const registerUserFlow = async (uid: string) => {
        try {
            const result = await dispatch(registerWithGoogle(uid)).unwrap();
            if (result.user?.status === '0' || !result.authorisation?.token) {
                setPendingUid(uid);
                // Navigate to the Phone Input Screen and pass pendingUid as a parameter
                router.push(`/auth/phonenumber?pendingUid=${uid}`);
                return null;
            }
            if (result.status && result.authorisation?.token) {
                await AsyncStorage.setItem('token', result.authorisation.token);
                await AsyncStorage.setItem('user', JSON.stringify(result.user));
                return result;
            }
            return null;
        } catch (error: any) {
            if (error.requiresPhone) {
                setPendingUid(uid);
                router.push(`/auth/phonenumber?pendingUid=${uid}`);
                return null;
            }
            throw error;
        }
    };


    const handleAuthResponse = async (response: any) => {
        try {
            // Keep loading states active to prevent showing login screen
            let idToken = null;
            let accessToken = null;

            // If tokens are returned directly (web)
            if (response.params.id_token) {
                idToken = response.params.id_token;
                accessToken = response.params.access_token;
            }
            // Otherwise, exchange the authorization code for tokens.
            else if (response.params.code) {
                try {
                    const clientId = getClientId();
                    const tokenResult = await exchangeCodeAsync(
                        {
                            code: response.params.code,
                            clientId,
                            redirectUri: makeRedirectUri({
                                scheme: 'com.factcoding.secondcar',
                                path: ''
                            }),
                            extraParams: {
                                code_verifier: request?.codeVerifier || ''
                            }
                        },
                        {
                            tokenEndpoint: 'https://oauth2.googleapis.com/token'
                        }
                    );
                    idToken = tokenResult.idToken;
                    accessToken = tokenResult.accessToken;
                } catch (exchangeError) {
                    console.log('Token exchange failed, trying manual exchange:', exchangeError);
                    throw new Error('Failed to exchange authorization code for tokens');
                }
            }

            if (!idToken) {
                throw new Error('No ID token received from Google authentication');
            }

            // Sign in to Firebase using the received tokens.
            const credential = GoogleAuthProvider.credential(idToken, accessToken);
            const userCredential = await signInWithCredential(auth, credential);
            const user = userCredential.user;

            // Register with backend using Redux action
            const registerResult = await registerUserFlow(user.uid);
            if (registerResult?.status && registerResult.authorisation?.token) {
                // Navigate directly to authenticated screen
                await router.replace('/(auth)/(tabs)');
            }
        } catch (error: any) {
            console.error('Google Sign-In Error:', error);
            Alert.alert('Authentication Error', error.message || 'Failed to complete Google sign-in');
        }
        // Note: Loading states are cleared in handleGoogleSignIn finally block
    };

    // Improved Google Sign-In function with better loading states
    const handleGoogleSignIn = async () => {
        try {
            setGoogleLoading(true);
            setLoader(true); // Show main loader to prevent showing login screen

            if (!request) {
                Alert.alert('Error', 'Authentication not ready. Please try again.');
                return;
            }

            const result = await promptAsync();
            if (result.type === 'success') {
                // Keep loading state active during authentication
                await handleAuthResponse(result);
            } else if (result.type === 'cancel') {
                // User cancelled, no need to show error
                console.log('Google Sign-In cancelled by user');
            } else {
                Alert.alert('Error', 'Google Sign-In was unsuccessful');
            }
        } catch (error: any) {
            Alert.alert('Error', error.message || 'Failed to open Google sign-in');
        } finally {
            setGoogleLoading(false);
            setLoader(false);
        }
    };

    // ----- Facebook Sign-In Functions -----

    const registerFacebookUserFlow = async (uid: string) => {
        try {
            // Use the same registerWithGoogle endpoint for Facebook authentication
            const result = await dispatch(registerWithGoogle(uid)).unwrap();
            if (result.user?.status === '0' || !result.authorisation?.token) {
                setPendingUid(uid);
                // Navigate to the Phone Input Screen and pass pendingUid as a parameter
                router.push(`/auth/phonenumber?pendingUid=${uid}`);
                return null;
            }
            if (result.status && result.authorisation?.token) {
                await AsyncStorage.setItem('token', result.authorisation.token);
                await AsyncStorage.setItem('user', JSON.stringify(result.user));
                return result;
            }
            return null;
        } catch (error: any) {
            if (error.requiresPhone) {
                setPendingUid(uid);
                router.push(`/auth/phonenumber?pendingUid=${uid}`);
                return null;
            }
            throw error;
        }
    };

    const handleFacebookAuthResponse = async (result: any) => {
        try {
            if (!result.authentication?.accessToken) {
                throw new Error('No access token received from Facebook authentication');
            }

            console.log('Facebook authentication successful, creating Firebase credential');

            // Sign in to Firebase using the received access token
            const credential = FacebookAuthProvider.credential(result.authentication.accessToken);
            const userCredential = await signInWithCredential(auth, credential);
            const user = userCredential.user;

            // Register with backend using Redux action
            const registerResult = await registerFacebookUserFlow(user.uid);
            if (registerResult?.status && registerResult.authorisation?.token) {
                // Navigate directly to authenticated screen
                router.replace('/(auth)/(tabs)');
            }
        } catch (error: any) {
            console.error('Facebook Sign-In Error:', error);
            Alert.alert('Authentication Error', error.message || 'Failed to complete Facebook sign-in');
        }
        // Note: Loading states are cleared in handleFacebookSignIn finally block
    };

    // Improved Facebook Sign-In function with better loading states
    const handleFacebookSignIn = async () => {
        try {
            setFacebookLoading(true);
            setLoader(true); // Show main loader to prevent showing login screen

            if (!facebookRequest) {
                Alert.alert('Error', 'Facebook authentication not ready. Please try again.');
                return;
            }

            const result = await facebookPromptAsync();
            if (result.type === 'success') {
                // Keep loading state active during authentication
                await handleFacebookAuthResponse(result);
            } else if (result.type === 'cancel') {
                // User cancelled, no need to show error
                console.log('Facebook Sign-In cancelled by user');
            } else {
                Alert.alert('Error', 'Facebook Sign-In was unsuccessful');
            }
        } catch (error: any) {
            Alert.alert('Error', error.message || 'Failed to open Facebook sign-in');
        } finally {
            setFacebookLoading(false);
            setLoader(false);
        }
    };

    const handlePhoneSubmit = async () => {
        if (!pendingUid || !phone.trim()) {
            Alert.alert('Input Error', 'Please enter a valid phone number.');
            return;
        }
        try {
            setLoader(true);
            const result = await dispatch(
                registerWithPhone({
                    uid: pendingUid,
                    phone: phone.trim()
                })
            ).unwrap();
            if (result.status && result.authorisation?.token) {
                await AsyncStorage.setItem('token', result.authorisation.token);
                await AsyncStorage.setItem('user', JSON.stringify(result.user));
                setPhoneRequired(false);
                setPendingUid(null);
                await router.replace('/(auth)/(tabs)');
            } else {
                throw new Error(result.message || 'Phone registration failed');
            }
        } catch (error: any) {
            Alert.alert('Error', error.message || 'Failed to register phone');
        } finally {
            setLoader(false);
        }
    };

    // Get app version for display
    const getAppVersion = () => {
        return Constants.expoConfig?.version || '1.0.0';
    };

    // Render Google and Facebook Sign-In buttons
    const renderSocialButtons = () => {
        return (
            <View style={styles.socialButtonsRow}>
                <ThemedButton
                    style={[styles.socialButton, { backgroundColor: COLORS.white }]}
                    onPress={handleGoogleSignIn}
                    disabled={googleLoading || facebookLoading || loader}
                >
                    <View style={styles.socialButtonContent}>
                        <Image
                            source={require('@/assets/images/googleImage.png')}
                            style={styles.socialIcon}
                        />
                        {googleLoading ? (
                            <View style={styles.loadingContainer}>
                                <ActivityIndicator color={COLORS.text.primary} style={styles.activityIndicator} />
                                <ThemedText style={[styles.socialButtonText, { color: COLORS.text.primary }]}>
                                    Signing in...
                                </ThemedText>
                            </View>
                        ) : (
                            <ThemedText style={[styles.socialButtonText, { color: COLORS.text.primary }]}>
                                Continue with Google
                            </ThemedText>
                        )}
                    </View>
                </ThemedButton>

                <ThemedButton
                    style={[styles.socialButton, { backgroundColor: '#1877F2' }]}
                    onPress={handleFacebookSignIn}
                    disabled={googleLoading || facebookLoading || loader}
                >
                    <View style={styles.socialButtonContent}>
                        <Ionicons
                            name="logo-facebook"
                            size={20}
                            color={COLORS.white}
                            style={styles.socialIcon}
                        />
                        {facebookLoading ? (
                            <View style={styles.loadingContainer}>
                                <ActivityIndicator color={COLORS.white} style={styles.activityIndicator} />
                                <ThemedText style={[styles.socialButtonText, { color: COLORS.white }]}>
                                    Signing in...
                                </ThemedText>
                            </View>
                        ) : (
                            <ThemedText style={[styles.socialButtonText, { color: COLORS.white }]}>
                                Continue with Facebook
                            </ThemedText>
                        )}
                    </View>
                </ThemedButton>
            </View>
        );
    };

    return (
        <KeyboardAvoidingView
            style={styles.container}
            behavior={Platform.OS === 'ios' ? 'padding' : undefined}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
        >
            <ScrollView
                contentContainerStyle={styles.scrollContent}
                keyboardShouldPersistTaps="handled"
                showsVerticalScrollIndicator={false}
            >
                <View style={styles.contentContainer}>
                    {/* Show back button for all steps except 'email' */}
                    {step !== 'email' && (
                        <BackButton onPress={() => {
                            // Handle different back navigation flows
                            switch (step) {
                                case 'login':
                                case 'register':
                                    setStep('email');
                                    break;
                                case 'verify':
                                    setStep('email');
                                    break;
                                case 'forgotPassword':
                                    setStep('login');
                                    break;
                                case 'verifyForgotPassword':
                                    setStep('forgotPassword');
                                    break;
                                case 'resetPassword':
                                    setStep('verifyForgotPassword');
                                    break;
                                default:
                                    setStep('email');
                            }
                        }} />
                    )}

                    <Image
                        source={require('@/assets/images/splash-icon.png')}
                        style={styles.logo}
                        resizeMode="contain"
                    />

                    {step === 'email' && (
                        <>
                            <EmailForm
                                onEmailSubmit={handleEmailSubmit}
                                onRegister={() => setStep('register')}
                                loading={loader}
                            />

                            <View style={styles.dividerContainer}>
                                <View style={styles.divider} />
                                <ThemedText style={styles.dividerText}>OR</ThemedText>
                                <View style={styles.divider} />
                            </View>

                            {renderSocialButtons()}
                        </>
                    )}

                    {step === 'login' && (
                        <LoginForm
                            email={email}
                            password={password}
                            setPassword={setPassword}
                            onLoginSuccess={handleLogin}
                            onForgotPassword={() => setStep('forgotPassword')}
                            loading={loader}
                        />
                    )}

                    {step === 'register' && (
                        <RegisterForm
                            email={email}
                            onRegisterSuccess={handleRegister}
                            loading={loader}
                        />
                    )}

                    {step === 'verify' && (
                        <VerifyOtpForm
                            email={email}
                            otp={otp}
                            setOtp={setOtp}
                            onVerifySuccess={handleVerifyOtp}
                            loading={loader}
                        />
                    )}

                    {step === 'forgotPassword' && (
                        <ForgotPasswordForm
                            email={email}
                            onSendOtpSuccess={handleForgotPassword}
                            loading={loader}
                        />
                    )}

                    {step === 'verifyForgotPassword' && (
                        <VerifyForgotPasswordOtpForm
                            email={email}
                            otp={otp}
                            setOtp={setOtp}
                            onVerifySuccess={handleVerifyForgotPasswordOtp}
                            loading={loader}
                        />
                    )}

                    {step === 'resetPassword' && (
                        <ResetPasswordForm
                            email={email}
                            otp={otp}
                            onResetSuccess={handleResetPassword}
                            loading={loader}
                        />
                    )}

                    {/* App Version Display */}
                    {step === 'email' && (
                        <View style={styles.versionContainer}>
                            <ThemedText style={styles.versionText}>
                                Version {getAppVersion()}
                            </ThemedText>
                        </View>
                    )}
                </View>
            </ScrollView>

            {/* Loading Overlay for Social Sign-In */}
            {(googleLoading || facebookLoading || loader) && (
                <View style={styles.loadingOverlay}>
                    <View style={styles.loadingContent}>
                        <ActivityIndicator size="large" color={COLORS.primary} />
                        <ThemedText style={styles.loadingText}>
                            {googleLoading ? 'Signing in with Google...' :
                                facebookLoading ? 'Signing in with Facebook...' :
                                    'Please wait...'}
                        </ThemedText>
                    </View>
                </View>
            )}
        </KeyboardAvoidingView>
    );
}

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: COLORS.background
    },
    scrollContent: {
        flexGrow: 1,
        justifyContent: 'center'
    },
    contentContainer: {
        flex: 1,
        padding: SPACING.xl,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: COLORS.background,
        position: 'relative', // Add this for absolute positioning of back button
    },
    logo: {
        width: width * 0.4,
        height: width * 0.4,
        marginBottom: SPACING.xl,
        backgroundColor: 'transparent'
    } as ImageStyle,
    dividerContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: SPACING.lg,
        width: '100%'
    },
    divider: {
        flex: 1,
        height: 1,
        backgroundColor: COLORS.border.secondary
    },
    dividerText: {
        marginHorizontal: SPACING.md,
        color: COLORS.text.secondary,
        fontSize: 14
    },
    // Social/Google sign-in styles (moved from SocialButtons)
    socialButtonsRow: {
        flexDirection: 'column',
        width: '100%',
        gap: 10
    },
    socialButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%',
        paddingVertical: SPACING.md,
        borderRadius: 8,
        marginVertical: 6,
        elevation: 2,
        shadowColor: COLORS.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
        borderWidth: 1,
        borderColor: COLORS.border.primary,
    } as ViewStyle,
    socialButtonContent: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 12,
        justifyContent: 'center',
        width: '100%',
    },
    socialIcon: {
        width: 24,
        height: 24
    },
    socialButtonText: {
        fontSize: 16,
        fontWeight: '600'
    },
    loadingContainer: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    activityIndicator: {
        marginRight: 8
    },
    phoneContainer: {
        padding: SPACING.md,
        width: '100%',
        backgroundColor: COLORS.white,
        borderRadius: 8,
        elevation: 2,
        shadowColor: COLORS.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3.84
    },
    label: {
        fontSize: 16,
        marginBottom: 16,
        fontWeight: '600',
        textAlign: 'center'
    },
    phoneInput: {
        borderWidth: 1,
        borderColor: COLORS.border.primary,
        borderRadius: 8,
        padding: SPACING.sm,
        marginBottom: SPACING.md,
        fontSize: 16,
        width: '100%',
        backgroundColor: COLORS.surface.secondary
    },
    backButton: {
        position: 'absolute',
        left: SPACING.md,
        top: Platform.OS === 'ios' ? 50 : 40,
        zIndex: 1,
        padding: SPACING.sm,
        backgroundColor: COLORS.primary,
        borderRadius: 25,
        flexDirection: 'row',
        alignItems: 'center',
        elevation: 4,
        shadowColor: COLORS.primary,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.3,
        shadowRadius: 4,
        borderWidth: 1,
        borderColor: 'rgba(255,255,255,0.2)',
    },
    backButtonText: {
        color: COLORS.white,
        fontSize: 14,
        fontWeight: '600',
        marginLeft: 2,
    },
    // App version styles
    versionContainer: {
        position: 'absolute',
        bottom: SPACING.lg,
        alignSelf: 'center',
    },
    versionText: {
        fontSize: 12,
        color: COLORS.text.secondary,
        textAlign: 'center',
    },
    // Loading overlay styles
    loadingOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000,
    },
    loadingContent: {
        backgroundColor: COLORS.white,
        padding: SPACING.xl,
        borderRadius: 12,
        alignItems: 'center',
        minWidth: 200,
    },
    loadingText: {
        marginTop: SPACING.md,
        fontSize: 16,
        color: COLORS.text.primary,
        textAlign: 'center',
    }
});
