import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { ThemedText } from '@/components/common/ThemedText';
import { COLORS, SPACING } from '@/constants/theme';
import { Ionicons } from '@expo/vector-icons';
import { Image } from 'react-native';

const { width } = Dimensions.get('window');

// Mock vehicle data for testing
const mockVehicle = {
    id: 1,
    title: 'Maruti Suzuki Alto',
    brand: 'Maruti Suzuki',
    model: 'Alto',
    variant: 'Lxi',
    year: '2018',
    fuel_type: 'Petrol/CNG',
    kilometers_driven: 88000,
    price: '225000',
    transmission: 'Manual',
    ownership: 'First',
    location: 'Gujarat',
    description: 'Well maintained car',
    images: ['https://example.com/car.jpg'],
    primary_image: 'https://example.com/car.jpg',
    insurance_type: 'comprehensive',
    user: { phone: '1234567890' }
};

// Styles for the shareable vehicle card
const shareableCardStyles = StyleSheet.create({
    container: {
        width: 350,
        backgroundColor: COLORS.white,
        borderRadius: 12,
        overflow: 'hidden',
        elevation: 4,
        shadowColor: COLORS.black,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: SPACING.md,
        backgroundColor: COLORS.primary,
    },
    logoContainer: {
        flex: 1,
    },
    logoText: {
        fontSize: 20,
        fontWeight: 'bold',
        color: COLORS.white,
    },
    priceContainer: {
        backgroundColor: COLORS.secondary,
        paddingHorizontal: SPACING.md,
        paddingVertical: SPACING.xs,
        borderRadius: 20,
    },
    price: {
        fontSize: 18,
        fontWeight: 'bold',
        color: COLORS.white,
    },
    vehicleImage: {
        width: '100%',
        height: 200,
    },
    detailsContainer: {
        padding: SPACING.md,
    },
    title: {
        fontSize: 18,
        fontWeight: 'bold',
        color: COLORS.text.primary,
        marginBottom: SPACING.sm,
        textAlign: 'center',
    },
    detailsGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'space-between',
        marginBottom: SPACING.md,
    },
    detailItem: {
        flexDirection: 'row',
        alignItems: 'center',
        width: '48%',
        marginBottom: SPACING.xs,
        gap: SPACING.xs,
    },
    detailText: {
        fontSize: 12,
        color: COLORS.text.secondary,
        flex: 1,
    },
    additionalDetails: {
        borderTopWidth: 1,
        borderTopColor: COLORS.border.secondary,
        paddingTop: SPACING.sm,
        marginBottom: SPACING.sm,
    },
    detailRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: SPACING.xs,
    },
    detailLabel: {
        fontSize: 12,
        color: COLORS.text.secondary,
        flex: 1,
    },
    detailValue: {
        fontSize: 12,
        color: COLORS.text.primary,
        fontWeight: '500',
        flex: 1,
        textAlign: 'right',
    },
    footer: {
        borderTopWidth: 1,
        borderTopColor: COLORS.border.secondary,
        paddingTop: SPACING.sm,
        alignItems: 'center',
    },
    footerText: {
        fontSize: 12,
        color: COLORS.text.secondary,
        fontStyle: 'italic',
    },
});

export default function ShareableCardTest() {
    return (
        <View style={styles.container}>
            <ThemedText style={styles.title}>Shareable Card Test</ThemedText>
            <View style={shareableCardStyles.container}>
                {/* Header with Logo */}
                <View style={shareableCardStyles.header}>
                    <View style={shareableCardStyles.logoContainer}>
                        <ThemedText style={shareableCardStyles.logoText}>2ndCar</ThemedText>
                    </View>
                    <View style={shareableCardStyles.priceContainer}>
                        <ThemedText style={shareableCardStyles.price}>
                            ₹{parseInt(mockVehicle.price).toLocaleString('en-IN')}
                        </ThemedText>
                    </View>
                </View>

                {/* Vehicle Image */}
                <Image
                    source={{
                        uri: 'https://placeholder.com/350x200',
                    }}
                    style={shareableCardStyles.vehicleImage}
                    resizeMode="cover"
                />

                {/* Vehicle Details */}
                <View style={shareableCardStyles.detailsContainer}>
                    <ThemedText style={shareableCardStyles.title}>{mockVehicle.title}</ThemedText>
                    
                    {/* Key Details Grid */}
                    <View style={shareableCardStyles.detailsGrid}>
                        <View style={shareableCardStyles.detailItem}>
                            <Ionicons name="calendar-outline" size={16} color={COLORS.text.secondary} />
                            <ThemedText style={shareableCardStyles.detailText}>{mockVehicle.year}</ThemedText>
                        </View>
                        <View style={shareableCardStyles.detailItem}>
                            <Ionicons name="speedometer-outline" size={16} color={COLORS.text.secondary} />
                            <ThemedText style={shareableCardStyles.detailText}>{mockVehicle.kilometers_driven} km</ThemedText>
                        </View>
                        <View style={shareableCardStyles.detailItem}>
                            <Ionicons name="car-outline" size={16} color={COLORS.text.secondary} />
                            <ThemedText style={shareableCardStyles.detailText}>{mockVehicle.fuel_type}</ThemedText>
                        </View>
                        <View style={shareableCardStyles.detailItem}>
                            <Ionicons name="person-outline" size={16} color={COLORS.text.secondary} />
                            <ThemedText style={shareableCardStyles.detailText}>{mockVehicle.ownership}</ThemedText>
                        </View>
                    </View>

                    {/* Additional Details */}
                    <View style={shareableCardStyles.additionalDetails}>
                        <View style={shareableCardStyles.detailRow}>
                            <ThemedText style={shareableCardStyles.detailLabel}>Brand:</ThemedText>
                            <ThemedText style={shareableCardStyles.detailValue}>{mockVehicle.brand}</ThemedText>
                        </View>
                        <View style={shareableCardStyles.detailRow}>
                            <ThemedText style={shareableCardStyles.detailLabel}>Model:</ThemedText>
                            <ThemedText style={shareableCardStyles.detailValue}>{mockVehicle.model}</ThemedText>
                        </View>
                        <View style={shareableCardStyles.detailRow}>
                            <ThemedText style={shareableCardStyles.detailLabel}>Variant:</ThemedText>
                            <ThemedText style={shareableCardStyles.detailValue}>{mockVehicle.variant}</ThemedText>
                        </View>
                        <View style={shareableCardStyles.detailRow}>
                            <ThemedText style={shareableCardStyles.detailLabel}>Location:</ThemedText>
                            <ThemedText style={shareableCardStyles.detailValue}>{mockVehicle.location}</ThemedText>
                        </View>
                    </View>

                    {/* Footer */}
                    <View style={shareableCardStyles.footer}>
                        <ThemedText style={shareableCardStyles.footerText}>
                            Find more cars at 2ndCar.in
                        </ThemedText>
                    </View>
                </View>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        padding: SPACING.md,
        backgroundColor: COLORS.background,
        alignItems: 'center',
        justifyContent: 'center',
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: SPACING.lg,
        color: COLORS.text.primary,
    },
});
